// Performance optimization utilities for Flowkar website

// Lazy loading configuration
const lazyLoadConfig = {
    rootMargin: '50px 0px',
    threshold: 0.01
};

// Image optimization and lazy loading
function initializeLazyLoading() {
    // Check if Intersection Observer is supported
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    loadImage(img);
                    observer.unobserve(img);
                }
            });
        }, lazyLoadConfig);

        // Observe all images with data-src attribute
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });

        // Observe all images with loading="lazy"
        document.querySelectorAll('img[loading="lazy"]').forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // Fallback for browsers without Intersection Observer
        loadAllImages();
    }
}

// Load individual image
function loadImage(img) {
    // Show loading placeholder
    img.style.filter = 'blur(5px)';
    img.style.transition = 'filter 0.3s ease';

    const imageLoader = new Image();
    imageLoader.onload = function() {
        // Replace src with data-src
        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        }
        
        // Remove blur effect
        img.style.filter = 'none';
        img.classList.add('loaded');
    };

    imageLoader.onerror = function() {
        // Handle image load error
        img.style.filter = 'none';
        img.classList.add('error');
        console.warn('Failed to load image:', img.dataset.src || img.src);
    };

    // Start loading
    imageLoader.src = img.dataset.src || img.src;
}

// Fallback: load all images immediately
function loadAllImages() {
    document.querySelectorAll('img[data-src]').forEach(img => {
        img.src = img.dataset.src;
        img.removeAttribute('data-src');
    });
}

// Preload critical images
function preloadCriticalImages() {
    const criticalImages = [
        'src/assets/logo.svg',
        'src/assets/hamburgerMenu.svg',
        'src/assets/close.svg',
        'src/assets/Management/Background.svg'
    ];

    criticalImages.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
    });
}

// Optimize animations for performance
function optimizeAnimations() {
    // Reduce motion for users who prefer it
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        document.documentElement.style.setProperty('--animation-duration', '0.01ms');
        document.documentElement.style.setProperty('--transition-duration', '0.01ms');
        return;
    }

    // Pause animations when page is not visible
    document.addEventListener('visibilitychange', function() {
        const animatedElements = document.querySelectorAll('[class*="animate-"]');
        
        if (document.hidden) {
            animatedElements.forEach(el => {
                el.style.animationPlayState = 'paused';
            });
        } else {
            animatedElements.forEach(el => {
                el.style.animationPlayState = 'running';
            });
        }
    });

    // Optimize for low-end devices
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
        document.documentElement.style.setProperty('--animation-duration', '0.2s');
        document.documentElement.style.setProperty('--transition-duration', '0.2s');
    }
}

// Debounce function for performance
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// Throttle function for performance
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Optimize scroll performance
function optimizeScrollPerformance() {
    let ticking = false;

    function updateScrollElements() {
        // Update scroll-dependent elements here
        ticking = false;
    }

    const optimizedScrollHandler = () => {
        if (!ticking) {
            requestAnimationFrame(updateScrollElements);
            ticking = true;
        }
    };

    window.addEventListener('scroll', optimizedScrollHandler, { passive: true });
}

// Resource hints for better loading
function addResourceHints() {
    // DNS prefetch for external resources
    const dnsPrefetchUrls = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com',
        'https://cdn.jsdelivr.net',
        'https://cdnjs.cloudflare.com'
    ];

    dnsPrefetchUrls.forEach(url => {
        const link = document.createElement('link');
        link.rel = 'dns-prefetch';
        link.href = url;
        document.head.appendChild(link);
    });

    // Preconnect to critical resources
    const preconnectUrls = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com'
    ];

    preconnectUrls.forEach(url => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = url;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
    });
}

// Monitor performance metrics
function monitorPerformance() {
    // Check if Performance Observer is supported
    if ('PerformanceObserver' in window) {
        // Monitor Largest Contentful Paint
        const lcpObserver = new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            const lastEntry = entries[entries.length - 1];
            console.log('LCP:', lastEntry.startTime);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // Monitor First Input Delay
        const fidObserver = new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            entries.forEach(entry => {
                console.log('FID:', entry.processingStart - entry.startTime);
            });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

        // Monitor Cumulative Layout Shift
        const clsObserver = new PerformanceObserver((entryList) => {
            let clsValue = 0;
            const entries = entryList.getEntries();
            entries.forEach(entry => {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            });
            console.log('CLS:', clsValue);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
}

// Service Worker registration for caching
function registerServiceWorker() {
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('/sw.js')
                .then(function(registration) {
                    console.log('ServiceWorker registration successful');
                })
                .catch(function(err) {
                    console.log('ServiceWorker registration failed');
                });
        });
    }
}

// Critical CSS inlining
function inlineCriticalCSS() {
    // This would typically be done at build time
    // For now, we ensure critical styles are loaded first
    const criticalStyles = document.querySelector('link[href*="styles.css"]');
    if (criticalStyles) {
        criticalStyles.setAttribute('rel', 'preload');
        criticalStyles.setAttribute('as', 'style');
        criticalStyles.setAttribute('onload', "this.onload=null;this.rel='stylesheet'");
    }
}

// Initialize all performance optimizations
function initializePerformanceOptimizations() {
    // Run immediately
    preloadCriticalImages();
    addResourceHints();
    optimizeAnimations();
    
    // Run when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            initializeLazyLoading();
            optimizeScrollPerformance();
            monitorPerformance();
        });
    } else {
        initializeLazyLoading();
        optimizeScrollPerformance();
        monitorPerformance();
    }
    
    // Run when page is fully loaded
    window.addEventListener('load', function() {
        registerServiceWorker();
        
        // Remove loading states
        document.body.classList.add('loaded');
        
        // Initialize intersection observers for animations
        if ('IntersectionObserver' in window) {
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, { threshold: 0.1 });
            
            document.querySelectorAll('.animate-on-scroll').forEach(el => {
                animationObserver.observe(el);
            });
        }
    });
}

// Export functions for use in other scripts
window.PerformanceUtils = {
    debounce,
    throttle,
    loadImage,
    preloadCriticalImages,
    initializeLazyLoading
};

// Auto-initialize
initializePerformanceOptimizations();
