# Flowkar Website - HTML/CSS/Bootstrap Conversion

This project is a complete conversion of the original React + Tailwind CSS Flowkar website to pure HTML, CSS, and Bootstrap 5.3.x. The converted website maintains pixel-perfect design fidelity while being fully responsive and performant.

## 🚀 Features

- **Pure HTML/CSS/Bootstrap**: No React or Tailwind dependencies
- **Fully Responsive**: Works perfectly on mobile, tablet, and desktop
- **Bootstrap 5.3.x**: Modern Bootstrap components and utilities
- **Performance Optimized**: Lazy loading, service worker, and optimized animations
- **Pixel-Perfect Design**: Maintains exact visual appearance of the original
- **Cross-Browser Compatible**: Works on all modern browsers
- **SEO Friendly**: Proper meta tags and semantic HTML structure

## 📁 Project Structure

```
├── index.html              # Main homepage
├── about.html              # About Us page
├── contact.html            # Contact page
├── blogs.html              # Blog listing page
├── terms.html              # Terms of Service
├── privacy.html            # Privacy Policy
├── css/
│   ├── styles.css          # Main styles and Bootstrap customizations
│   ├── animations.css      # Animation keyframes and classes
│   └── components.css      # Component-specific styles
├── js/
│   ├── main.js             # Main application logic
│   ├── navigation.js       # Navigation and routing functionality
│   ├── animations.js       # Animation utilities and handlers
│   └── performance.js      # Performance optimizations
├── src/assets/             # Original asset files (images, icons, etc.)
├── sw.js                   # Service Worker for caching
└── README.md               # This file
```

## 🛠️ Technologies Used

- **HTML5**: Semantic markup and modern HTML features
- **CSS3**: Custom properties, flexbox, grid, and animations
- **Bootstrap 5.3.x**: Component library and responsive grid system
- **Vanilla JavaScript**: No frameworks, pure JavaScript functionality
- **Service Worker**: Offline functionality and caching
- **Intersection Observer**: Lazy loading and scroll animations
- **Web Performance APIs**: Performance monitoring and optimization

## 🎨 Design Features

### Original Design Elements Preserved:
- ✅ Hero section with animated image gallery
- ✅ Management features section with background image
- ✅ Social media icons with hover effects
- ✅ Info cards with feature highlights
- ✅ About Us section with team images
- ✅ Testimonials carousel
- ✅ FAQ accordion
- ✅ Footer with multi-column layout
- ✅ Custom button styles and hover effects
- ✅ Gradient text effects
- ✅ Smooth scrolling animations

### Responsive Breakpoints:
- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1199px
- **Desktop**: 1200px and above

## 🚀 Getting Started

### Prerequisites
- A modern web browser
- A local web server (for development)

### Installation

1. **Clone or download the project files**
2. **Serve the files using a local web server**

#### Option 1: Using Python (if installed)
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### Option 2: Using Node.js (if installed)
```bash
# Install a simple server
npm install -g http-server

# Run the server
http-server -p 8000
```

#### Option 3: Using PHP (if installed)
```bash
php -S localhost:8000
```

#### Option 4: Using Live Server (VS Code Extension)
- Install the "Live Server" extension in VS Code
- Right-click on `index.html` and select "Open with Live Server"

3. **Open your browser and navigate to `http://localhost:8000`**

## 📱 Responsive Design

The website is fully responsive and adapts to different screen sizes:

### Mobile (≤ 767px)
- Stacked layout for all sections
- Mobile-optimized navigation with hamburger menu
- Touch-friendly buttons and interactions
- Optimized image sizes and animations

### Tablet (768px - 1199px)
- Two-column layouts where appropriate
- Adjusted font sizes and spacing
- Optimized for touch interactions

### Desktop (≥ 1200px)
- Full multi-column layouts
- Hover effects and animations
- Optimized for mouse interactions
- Maximum content width for readability

## ⚡ Performance Features

### Optimization Techniques:
- **Lazy Loading**: Images load only when needed
- **Service Worker**: Caches resources for offline access
- **Resource Hints**: DNS prefetch and preconnect for faster loading
- **Critical CSS**: Important styles loaded first
- **Optimized Animations**: Reduced motion support and performance monitoring
- **Image Optimization**: Proper sizing and format selection
- **Code Splitting**: Separate files for different functionality

## 🎯 Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+
- **Mobile Browsers**: iOS Safari 14+, Chrome Mobile 90+

## 🔧 Customization

### Colors
The main brand colors are defined as CSS custom properties in `css/styles.css`:
```css
:root {
    --primary-brown: #563D39;
    --primary-brown-hover: #4a332f;
    --primary-brown-light: #95725D;
    --text-gray: #939393;
    --text-black-light: #00000099;
}
```

### Fonts
The website uses the Figtree font family from Google Fonts. You can change this in the HTML head section or CSS.

### Animations
Animation speeds and effects can be customized in `css/animations.css`. The website respects the user's `prefers-reduced-motion` setting.

## 🚀 Deployment

The website can be deployed to any static hosting service:

### Recommended Hosting Options:
- **Netlify**: Drag and drop deployment
- **Vercel**: Git-based deployment
- **GitHub Pages**: Free hosting for public repositories
- **AWS S3**: Scalable static hosting
- **Cloudflare Pages**: Fast global CDN

### Deployment Steps:
1. Upload all files to your hosting service
2. Ensure the web server serves the correct MIME types
3. Configure HTTPS (most hosts do this automatically)
4. Test all functionality after deployment

## 🐛 Troubleshooting

### Common Issues:

1. **Images not loading**: Check file paths and ensure assets are uploaded
2. **Animations not working**: Verify JavaScript files are loaded correctly
3. **Mobile menu not working**: Ensure Bootstrap JavaScript is loaded
4. **Styles not applying**: Check CSS file paths and loading order

### Debug Mode:
Open browser developer tools (F12) to check for:
- Console errors
- Network request failures
- CSS loading issues
- JavaScript errors

---

**Note**: This is a static HTML/CSS/Bootstrap conversion of the original React application. All dynamic functionality has been replicated using vanilla JavaScript while maintaining the exact visual appearance and user experience of the original design.
