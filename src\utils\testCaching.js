/**
 * Caching System Test Utility
 * Use this to test and verify the caching implementation
 */

import assetPreloader from './assetPreloader';

/**
 * Test the asset preloader functionality
 */
export const testAssetPreloader = async () => {
  console.log('🧪 Testing Asset Preloader...');
  
  try {
    // Test basic preloading
    const testAssets = [
      '/logo.svg',
      '/favicon.ico'
    ];
    
    console.log('📥 Preloading test assets...');
    await assetPreloader.preloadCriticalAssets(testAssets);
    
    // Check cache stats
    const stats = assetPreloader.getCacheStats();
    console.log('📊 Cache Stats:', stats);
    
    // Test individual asset loading
    console.log('🖼️ Testing individual asset loading...');
    const asset = await assetPreloader.getAsset('/logo.svg');
    console.log('✅ Asset loaded:', asset);
    
    console.log('✅ Asset Preloader test completed successfully!');
    return true;
  } catch (error) {
    console.error('❌ Asset Preloader test failed:', error);
    return false;
  }
};

/**
 * Test service worker registration
 */
export const testServiceWorker = () => {
  console.log('🧪 Testing Service Worker...');
  
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready.then((registration) => {
      console.log('✅ Service Worker is ready:', registration);
      return true;
    }).catch((error) => {
      console.error('❌ Service Worker test failed:', error);
      return false;
    });
  } else {
    console.warn('⚠️ Service Worker not supported in this browser');
    return false;
  }
};

/**
 * Test cache storage
 */
export const testCacheStorage = async () => {
  console.log('🧪 Testing Cache Storage...');
  
  try {
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      console.log('📦 Available caches:', cacheNames);
      
      // Test cache operations
      const testCache = await caches.open('test-cache');
      const testResponse = new Response('Test data');
      await testCache.put('/test', testResponse);
      
      const cachedResponse = await testCache.match('/test');
      if (cachedResponse) {
        console.log('✅ Cache storage test successful');
        await caches.delete('test-cache'); // Cleanup
        return true;
      } else {
        console.error('❌ Failed to retrieve cached response');
        return false;
      }
    } else {
      console.warn('⚠️ Cache API not supported in this browser');
      return false;
    }
  } catch (error) {
    console.error('❌ Cache storage test failed:', error);
    return false;
  }
};

/**
 * Test performance monitoring
 */
export const testPerformanceMonitoring = () => {
  console.log('🧪 Testing Performance Monitoring...');
  
  try {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0];
      const paint = performance.getEntriesByType('paint');
      
      console.log('⏱️ Navigation timing:', {
        domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.domContentLoadedEventStart,
        loadComplete: navigation?.loadEventEnd - navigation?.loadEventStart
      });
      
      console.log('🎨 Paint timing:', paint.map(p => ({ name: p.name, time: p.startTime })));
      
      console.log('✅ Performance monitoring test successful');
      return true;
    } else {
      console.warn('⚠️ Performance API not supported in this browser');
      return false;
    }
  } catch (error) {
    console.error('❌ Performance monitoring test failed:', error);
    return false;
  }
};

/**
 * Run all caching tests
 */
export const runAllTests = async () => {
  console.log('🚀 Running all caching system tests...');
  
  const results = {
    assetPreloader: await testAssetPreloader(),
    serviceWorker: testServiceWorker(),
    cacheStorage: await testCacheStorage(),
    performanceMonitoring: testPerformanceMonitoring()
  };
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`📋 Test Results: ${passedTests}/${totalTests} tests passed`);
  console.log('📊 Detailed results:', results);
  
  if (passedTests === totalTests) {
    console.log('🎉 All caching system tests passed!');
  } else {
    console.warn('⚠️ Some tests failed. Check the console for details.');
  }
  
  return results;
};

/**
 * Quick performance benchmark
 */
export const benchmarkCaching = async () => {
  console.log('⚡ Running caching performance benchmark...');
  
  const testAssets = [
    '/logo.svg',
    '/favicon.ico'
  ];
  
  // Test without cache
  const startTime = performance.now();
  
  try {
    // First load (no cache)
    await Promise.all(testAssets.map(asset => 
      fetch(asset).then(response => response.blob())
    ));
    const firstLoadTime = performance.now() - startTime;
    
    // Preload assets
    await assetPreloader.preloadCriticalAssets(testAssets);
    
    // Second load (with cache)
    const cacheStartTime = performance.now();
    await Promise.all(testAssets.map(asset => assetPreloader.getAsset(asset)));
    const cacheLoadTime = performance.now() - cacheStartTime;
    
    const improvement = ((firstLoadTime - cacheLoadTime) / firstLoadTime) * 100;
    
    console.log('📈 Benchmark Results:');
    console.log(`   First load: ${firstLoadTime.toFixed(2)}ms`);
    console.log(`   Cached load: ${cacheLoadTime.toFixed(2)}ms`);
    console.log(`   Improvement: ${improvement.toFixed(1)}%`);
    
    return {
      firstLoadTime,
      cacheLoadTime,
      improvement
    };
  } catch (error) {
    console.error('❌ Benchmark failed:', error);
    return null;
  }
};

// Auto-run tests in development mode
if (import.meta.env.DEV) {
  // Run tests after a short delay to allow the app to initialize
  setTimeout(() => {
    console.log('🔧 Development mode detected - running caching tests...');
    runAllTests();
  }, 2000);
}
