<?xml version="1.0" encoding="UTF-8"?>
<svg id="Capa_1" data-name="Capa 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 74 74">
  <defs>
    <style>
      .cls-1 {
        fill: #069;
      }

      .cls-2 {
        fill: #fff;
        fill-rule: evenodd;
      }
    </style>
  </defs>
  <circle class="cls-1" cx="37" cy="37" r="37"/>
  <path id="path28" class="cls-2" d="M25.9,56.77V30.09h-8.87v26.68h8.87Zm-4.43-30.32c3.09,0,5.02-2.05,5.02-4.61-.06-2.62-1.92-4.61-4.96-4.61s-5.02,1.99-5.02,4.61,1.92,4.61,4.9,4.61h.06Z"/>
  <path id="path30" class="cls-2" d="M30.81,56.77h8.87v-14.9c0-.8,.06-1.59,.29-2.16,.64-1.59,2.1-3.24,4.55-3.24,3.21,0,4.49,2.45,4.49,6.03v14.27h8.87v-15.3c0-8.19-4.37-12.01-10.21-12.01-4.78,0-6.88,2.67-8.05,4.49h.06v-3.87h-8.87c.12,2.5,0,26.68,0,26.68h0Z"/>
</svg>