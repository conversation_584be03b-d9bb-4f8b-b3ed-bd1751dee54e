// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
  // Initialize any Bootstrap components that require JavaScript
  initBootstrapComponents();
  
  // Handle smooth scrolling for anchor links
  initSmoothScrolling();
  
  // Handle login and signup redirects
  initAuthRedirects();
  
  // Load hero section images
  loadHeroImages();
});

/**
 * Initialize Bootstrap components that require JavaScript
 */
function initBootstrapComponents() {
  // Initialize tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
  
  // Initialize popovers
  const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
  popoverTriggerList.map(function (popoverTriggerEl) {
    return new bootstrap.Popover(popoverTriggerEl);
  });
}

/**
 * Initialize smooth scrolling for anchor links
 */
function initSmoothScrolling() {
  // Get all links that have hash (#) but not just #
  const links = document.querySelectorAll('a[href*="#"]:not([href="#"])');
  
  links.forEach(link => {
    link.addEventListener('click', function(e) {
      // Only prevent default if the link is on the same page
      if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && location.hostname === this.hostname) {
        e.preventDefault();
        
        // Get the target element
        const targetId = this.getAttribute('href').split('#')[1];
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          // Calculate position with offset for fixed header
          const headerOffset = 100;
          const elementPosition = targetElement.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
          
          // Smooth scroll to target
          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });
        }
      }
    });
  });
}

/**
 * Handle login and signup redirects
 */
function initAuthRedirects() {
  // Login button click handler
  const loginButtons = document.querySelectorAll('[data-action="login"]');
  loginButtons.forEach(button => {
    button.addEventListener('click', function() {
      handleRedirect();
    });
  });
  
  // Sign up button click handler
  const signupButtons = document.querySelectorAll('[data-action="signup"]');
  signupButtons.forEach(button => {
    button.addEventListener('click', function() {
      window.location.href = 'https://app.flowkar.com/sign-up';
    });
  });
}

/**
 * Handle login redirect based on user data
 */
function handleRedirect() {
  const userdataString = localStorage.getItem('UserId');
  if (userdataString) {
    try {
      const userdata = JSON.parse(userdataString);
      // Check if userdata exists and has required properties
      if (userdata) {
        window.location.href = 'https://app.flowkar.com/dashboard';
      } else {
        window.location.href = 'https://app.flowkar.com/sign-in';
      }
    } catch (error) {
      console.error('Error parsing user data:', error);
      window.location.href = 'https://app.flowkar.com/sign-in';
    }
  } else {
    window.location.href = 'https://app.flowkar.com/sign-in';
  }
}

/**
 * Load hero section images
 */
function loadHeroImages() {
  // Configuration for hero images
  const imageConfigs = {
    desktop: {
      leftColumn: [
        { src: '../src/assets/Hero/Pinterest.svg', alt: 'Pinterest', height: '169px', width: '250px' },
        { src: '../src/assets/Hero/Dummy-01.svg', alt: 'Dummy', height: '310px', width: '250px' },
        { src: '../src/assets/Hero/Instagram.svg', alt: 'Instagram', height: '173px', width: '250px' },
        { src: '../src/assets/Hero/Dummy-02.svg', alt: 'Dummy', height: '250px', width: '250px' },
      ],
      rightColumn: [
        { src: '../src/assets/Hero/Dummy-03.svg', alt: 'Dummy-03', height: '250px', width: '250px' },
        { src: '../src/assets/Hero/LinkedIn.svg', alt: 'LinkedIn', height: '169px', width: '250px' },
        { src: '../src/assets/Hero/Dummy-04.svg', alt: 'Dummy-04', height: '250px', width: '250px' },
        { src: '../src/assets/Hero/Dummy-05.svg', alt: 'Dummy-05', height: '240px', width: '250px' },
      ],
    },
    mobile: {
      row1: [
        { src: '../src/assets/Hero/Pinterest.svg', alt: 'Pinterest' },
        { src: '../src/assets/Hero/Dummy-01.svg', alt: 'Dummy' },
        { src: '../src/assets/Hero/Instagram.svg', alt: 'Instagram' },
        { src: '../src/assets/Hero/Dummy-02.svg', alt: 'Dummy' },
      ],
      row2: [
        { src: '../src/assets/Hero/Dummy-03.svg', alt: 'Dummy-03' },
        { src: '../src/assets/Hero/LinkedIn.svg', alt: 'LinkedIn' },
        { src: '../src/assets/Hero/Dummy-04.svg', alt: 'Dummy-04' },
        { src: '../src/assets/Hero/Dummy-05.svg', alt: 'Dummy-05' },
      ],
    },
  };

  // Create desktop hero images
  const heroImagesContainer = document.querySelector('.hero-images');
  if (heroImagesContainer) {
    // Create container for the columns
    const columnsContainer = document.createElement('div');
    columnsContainer.className = 'd-flex justify-content-between';
    heroImagesContainer.appendChild(columnsContainer);

    // Create left column
    const leftColumn = document.createElement('div');
    leftColumn.className = 'd-flex flex-column me-3';
    columnsContainer.appendChild(leftColumn);

    // Create right column
    const rightColumn = document.createElement('div');
    rightColumn.className = 'd-flex flex-column ms-3';
    columnsContainer.appendChild(rightColumn);

    // Add images to left column
    imageConfigs.desktop.leftColumn.forEach(img => {
      const imgElement = document.createElement('img');
      imgElement.src = img.src;
      imgElement.alt = img.alt;
      imgElement.className = 'hero-image mb-3';
      imgElement.style.width = img.width;
      imgElement.style.height = img.height;
      leftColumn.appendChild(imgElement);
    });

    // Add images to right column
    imageConfigs.desktop.rightColumn.forEach(img => {
      const imgElement = document.createElement('img');
      imgElement.src = img.src;
      imgElement.alt = img.alt;
      imgElement.className = 'hero-image mb-3';
      imgElement.style.width = img.width;
      imgElement.style.height = img.height;
      rightColumn.appendChild(imgElement);
    });
  }

  // Create mobile hero images
  const mobileContainer = document.querySelector('.col-12.d-md-none.mt-5');
  if (mobileContainer) {
    // Create first row
    const row1Container = document.createElement('div');
    row1Container.className = 'scroll-container mb-4';
    mobileContainer.appendChild(row1Container);

    const row1Content = document.createElement('div');
    row1Content.className = 'scroll-left';
    row1Container.appendChild(row1Content);

    // Triple the images for seamless scrolling
    const tripleRow1 = [...imageConfigs.mobile.row1, ...imageConfigs.mobile.row1, ...imageConfigs.mobile.row1];
    tripleRow1.forEach(img => {
      const imgContainer = document.createElement('div');
      imgContainer.className = 'flex-shrink-0 me-4';
      
      const imgElement = document.createElement('img');
      imgElement.src = img.src;
      imgElement.alt = img.alt;
      imgElement.className = 'hero-image-round';
      imgElement.style.width = '120px';
      imgElement.style.height = '120px';
      
      imgContainer.appendChild(imgElement);
      row1Content.appendChild(imgContainer);
    });

    // Create second row
    const row2Container = document.createElement('div');
    row2Container.className = 'scroll-container';
    mobileContainer.appendChild(row2Container);

    const row2Content = document.createElement('div');
    row2Content.className = 'scroll-right';
    row2Container.appendChild(row2Content);

    // Triple the images for seamless scrolling
    const tripleRow2 = [...imageConfigs.mobile.row2, ...imageConfigs.mobile.row2, ...imageConfigs.mobile.row2];
    tripleRow2.forEach(img => {
      const imgContainer = document.createElement('div');
      imgContainer.className = 'flex-shrink-0 me-4';
      
      const imgElement = document.createElement('img');
      imgElement.src = img.src;
      imgElement.alt = img.alt;
      imgElement.className = 'hero-image-round';
      imgElement.style.width = '120px';
      imgElement.style.height = '120px';
      
      imgContainer.appendChild(imgElement);
      row2Content.appendChild(imgContainer);
    });
  }
}