import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export const setApiMessage = (type, message) => {
  // return () => {
  var commonProps = {
    position: "top-right",
    autoClose: 4000,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    hideProgressBar: false,
    theme: "dark",
    style: {
      fontFamily: "Figtree, sans-serif",
      borderRadius: "12px",
      minHeight: "unset",
      fontSize: "16px",
      fontWeight: "500",
      padding: "16px 20px",
      boxShadow: "0 4px 24px 0 rgba(86, 61, 57, 0.15)",
      border: "1px solid rgba(86, 61, 57, 0.1)",
    },
    toastId: "active",
  };
  switch (type) {
    case "info":
      toast.info(message, {
        ...commonProps,
        style: {
          ...commonProps.style,
          background: "#563D39",
          color: "#FFFFFF",
        //   borderLeft: "4px solid #563D39",
        },
      });
      break;
    case "success":
      toast.success(message, {
        ...commonProps,
        style: {
          ...commonProps.style,
          background: "#563D39",    
          color: "#FFFFFF",
        //   borderLeft: "4px solid #563D39",
        },
      });
      break;
    case "warning":
      toast.warning(message, {
        ...commonProps,
        style: {
          ...commonProps.style,
          background: "#563D39",
          color: "#FFFFFF",
        //   borderLeft: "4px solid #fbbf24",
        },
      });
      break;
    case "error":
      toast.error(message, {
        ...commonProps,
        style: {
          ...commonProps.style,
          background: "#563D39",
          color: "#FFFFFF",
        //   borderLeft: "4px solid #ef4444",
        },
      });
      break;
    default:
      break;
  }
  // toast.clearWaitingQueue();
  // };
};
