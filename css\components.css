/* Component-Specific Styles */

/* Blog Content Styles */
.content-container {
    color: #3d2a26;
    max-width: 1100px;
    margin: 0 auto;
    line-height: 1.8;
    padding: 0 2em;
}

.content-container p {
    font-size: 1.15rem;
    margin-bottom: 1.5em;
}

.content-container ul,
.content-container ol {
    font-size: 1.1rem;
    margin: 1.5em 0;
    padding-left: 1.5em;
}

.content-container li {
    margin-bottom: 0.75em;
}

.content-container h1 {
    font-size: 2.5em;
    margin: 1.5em 0 1em;
    color: #563D39;
    font-weight: bold;
}

.content-container h2 {
    font-size: 2em;
    margin: 1.5em 0 1em;
    color: #563D39;
    font-weight: bold;
}

.content-container h3 {
    font-size: 1.5em;
    margin: 1.5em 0 1em;
    color: #563D39;
    font-weight: bold;
}

.content-container strong,
.content-container b {
    color: #563D39;
}

.content-container blockquote {
    font-size: 1.25rem;
    line-height: 1.6;
    border-left: 4px solid #563D39;
    background: #f8f6f5;
    padding: 1.25em 1.5em;
    margin: 2em 0;
    border-radius: 0.5em;
    color: #563D39;
}

.content-container pre {
    font-size: 1rem;
    line-height: 1.6;
    background: #f3f4f6;
    padding: 1em;
    border-radius: 0.5em;
    margin: 2em 0;
    overflow-x: auto;
}

.content-container img {
    margin: 2em auto;
    border-radius: 0.75em;
    box-shadow: 0 4px 24px 0 rgba(86,61,57,0.08);
    max-width: 100%;
    height: auto;
    display: block;
}

.hero-image {
    width: 100%;
    max-width: 100%;
    aspect-ratio: 16 / 4.5;
    object-fit: cover;
    border-radius: 2em;
    box-shadow: 0 4px 24px 0 rgba(86, 61, 57, 0.08);
    display: block;
    margin: 0 auto;
}

.meta-info {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75em;
    justify-content: center;
    margin: 1.5em 0 2em 0;
}

.meta-badge {
    background: #563D39;
    color: #fff;
    padding: 0.4em 1.2em;
    border-radius: 999px;
    font-size: 0.95em;
    font-weight: 500;
    opacity: 0.92;
}

/* Image Gallery Styles */
.image-gallery {
    overflow: hidden;
    position: relative;
}

.image-gallery-mobile {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding-bottom: 20px;
}

.image-gallery-desktop {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

.gallery-column {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin: 0 0.5rem;
}

.gallery-row {
    display: flex;
    width: max-content;
    height: 120px;
    overflow: hidden;
}

.gallery-image {
    flex-shrink: 0;
    margin-right: 1rem;
    border-radius: 50%;
    object-fit: cover;
    will-change: transform;
    backface-visibility: hidden;
}

.gallery-image-mobile {
    width: 120px;
    height: 120px;
}

.gallery-image-desktop {
    width: 250px;
    border-radius: 1rem;
    object-fit: cover;
    margin-bottom: 0.75rem;
}

/* Skeleton Loaders */
.skeleton-circle {
    width: 120px;
    height: 120px;
    background-color: #e5e7eb;
    border-radius: 50%;
    margin-right: 1rem;
}

.skeleton-rect {
    width: 250px;
    background-color: #e5e7eb;
    border-radius: 1rem;
    margin-bottom: 0.75rem;
}

/* Feature Cards */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
}

.feature-card-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 1.5rem;
}

.feature-card-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.feature-card-description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
}

/* Social Media Icons */
.social-icons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.social-icon:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.2);
}

/* Testimonial Cards */
.testimonial-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
}

.testimonial-name {
    font-weight: 600;
    color: #563D39;
    margin-bottom: 0.5rem;
}

.testimonial-role {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.testimonial-text {
    font-style: italic;
    line-height: 1.6;
    color: #374151;
}

/* FAQ Accordion */
.faq-item {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.faq-question {
    background: #f9fafb;
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #563D39;
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background: #f3f4f6;
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-answer.active {
    padding: 1.5rem;
    max-height: 200px;
}

.faq-icon {
    transition: transform 0.3s ease;
}

.faq-icon.active {
    transform: rotate(180deg);
}

/* Mobile Responsive Adjustments */
@media (max-width: 1024px) {
    .content-container {
        padding: 0 1em;
    }
    .hero-image {
        max-height: 350px;
    }
    .feature-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .content-container {
        padding: 0 0.5em;
    }
    .content-container p,
    .content-container ul,
    .content-container ol {
        font-size: 1rem;
    }
    .content-container h1 {
        font-size: 2em;
    }
    .content-container h2 {
        font-size: 1.5em;
    }
    .content-container h3 {
        font-size: 1.2em;
    }
    .hero-image {
        max-height: 220px;
        border-radius: 1em !important;
    }
    .meta-info {
        flex-direction: column;
        align-items: center;
        gap: 0.5em;
    }
    .image-gallery-desktop {
        display: none;
    }
    .image-gallery-mobile {
        display: flex;
    }
}

@media (max-width: 480px) {
    .content-container {
        padding: 0 0.25em;
    }
    .content-container p,
    .content-container ul,
    .content-container ol {
        font-size: 0.95rem;
    }
    .content-container h1 {
        font-size: 1.3em;
    }
    .content-container h2 {
        font-size: 1.1em;
    }
    .content-container h3 {
        font-size: 1em;
    }
    .meta-info {
        flex-direction: column;
        align-items: stretch;
        gap: 0.4em;
    }
    .meta-badge {
        font-size: 0.85em;
        padding: 0.3em 0.8em;
    }
    .feature-card {
        height: auto;
        min-height: 280px;
    }
}

/* Hide desktop gallery on mobile */
@media (max-width: 767px) {
    .image-gallery-desktop {
        display: none !important;
    }
}

/* Hide mobile gallery on desktop */
@media (min-width: 768px) {
    .image-gallery-mobile {
        display: none !important;
    }
}
