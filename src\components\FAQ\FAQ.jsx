import React, { useState } from "react";
import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import leftArrow from "../../assets/Navbar/leftArrow.svg";
import Minus from "../../assets/FAQ/Minus.svg";
import Plus from "../../assets/FAQ/Plus.svg";
import { setApiMessage } from "../Toaster/setApiMessage";

const FAQ = () => {
  const [activeIndex, setActiveIndex] = useState(null);
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [emailError, setEmailError] = useState("");

  const toggleAccordion = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (e) => {
    const value = e.target.value;
    setEmail(value);

    // Clear error when user starts typing
    if (emailError) {
      setEmailError("");
    }
  };

  const handleEmailSubmit = async () => {
    // Clear any previous errors
    setEmailError("");

    // Trim whitespace from email
    const trimmedEmail = email.trim();

    // Check if email is empty
    if (!trimmedEmail) {
      setEmailError("Please enter your email address");
      // setApiMessage("error", "Please enter your email address");
      return;
    }

    // Check if email format is valid
    if (!validateEmail(trimmedEmail)) {
      setEmailError("Please enter a valid email address");
      // setApiMessage("error", "Please enter a valid email address");
      return;
    }

    // Check email length (reasonable limits)
    if (trimmedEmail.length > 254) {
      setEmailError("Email address is too long");
      setApiMessage("error", "Email address is too long");
      return;
    }

    setIsLoading(true);

    // Generate random delay between 1-2 seconds
    const randomDelay = Math.floor(Math.random() * 1000) + 1000; // 1000–2000ms

    try {
      await new Promise((resolve) => setTimeout(resolve, randomDelay));

      // Here you can add actual email submission logic
      console.log("Email submitted:", trimmedEmail);
      setApiMessage("success", "Thank you for your interest!");
      setEmail(""); // Clear the email input
      setEmailError(""); // Clear any errors
    } catch (error) {
      console.error("Error submitting email:", error);
      setApiMessage("error", "Something went wrong. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const data = [
    {
      title: "What is Flowkar?",
      content:
        "Flowkar is an all-in-one social media management tool.It lets you schedule, publish, and manage content from a single dashboard.Perfect for streamlining workflows and boosting online presence.",
    },
    {
      title: "What platforms does Flowkar support?",
      content:
        "Flowkar supports Instagram, Facebook, LinkedIn, Youtube, Pinterest, Thread, Vimeo and more.It helps you manage multiple accounts from one place.",
    },
    {
      title: "Can I view analytics for each platform in one report?",
      content:
        "Yes, Flowkar offers a unified analytics dashboard.You get both an overview and detailed performance reports per platform.Track growth, engagement, and optimize your strategy easily.",
    },
  ];

  return (
    <div className="w-[90%] mx-auto mt-10 p-6 sm:px-4">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
        {/* Left Section - Newsletter Signup */}
        <div className="bg-white  rounded-lg">
          <h2 className="text-[36px] font-medium text-[#563D39] mb-4">
            How We Can Help You?
          </h2>
          <p className="text-[#********] font-normal text-[16px] mb-6 leading-relaxed">
            Follow our newsletter. We will regularly update our latest project
            and availability.
          </p>

          <div className="flex flex-col gap-2 mb-6">
            <div className="flex flex-col md:flex-row gap-2">
              <input
                type="email"
                placeholder="Enter Your Email"
                value={email}
                onChange={handleEmailChange}
                disabled={isLoading}
                className={`flex-1 pt-[12px] pr-[12px] pb-[12px] pl-[14px] w-full md:w-[415px] h-[43px] bg-[#F6F6F6] rounded-[8px] focus:outline-none focus:ring-2 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${
                  emailError
                    ? "focus:ring-red-500 border-red-300"
                    : "focus:ring-[#563D39]"
                }`}
              />
              <button
                onClick={handleEmailSubmit}
                disabled={isLoading}
                className="bg-[#563D39] text-white font-normal text-[16px] rounded-[6px] pt-[6px] pb-[6px] pr-[6px] pl-[20px] hover:bg-[#4a332f] transition-colors flex items-center justify-around w-[141px] h-[42px] gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    Let's Talk
                    <img
                      src={leftArrow}
                      alt="Arrow"
                      className="w-[30px] h-[30px]"
                    />
                  </>
                )}
              </button>
            </div>

            {/* Error message display */}
            {emailError && (
              <p className="text-red-500 text-sm mt-1 font-medium">
                {emailError}
              </p>
            )}
          </div>

          <a href="#" className="text-[#563D39] hover:underline font-medium">
            More FAQ →
          </a>
        </div>

        {/* Right Section - FAQ Accordion */}
        <div className="space-y-0">
          {data.map((item, index) => (
            <div key={index}>
              <button
                aria-expanded={activeIndex === index}
                className="w-full flex justify-between items-center md:px-6 py-4 bg-white hover:bg-gray-50 transition-colors text-left"
                onClick={() => toggleAccordion(index)}
              >
                <span className="text-[20px] font-light text-[#000000]">
                  {item.title}
                </span>
                <div className="flex-shrink-0 ml-4">
                  <div className="w-8 h-8 rounded-full flex items-center justify-center">
                    {activeIndex === index ? (
                      <img
                        src={Minus}
                        alt="Minus"
                        className="w-[28px] h-[28px]"
                      />
                    ) : (
                      <img
                        src={Plus}
                        alt="Plus"
                        className="w-[28px] h-[28px]"
                      />
                    )}
                  </div>
                </div>
              </button>
              <div
                className={`overflow-hidden transition-all duration-500 ease-in-out ${
                  activeIndex === index
                    ? "max-h-[500px] opacity-100"
                    : "max-h-0 opacity-0"
                }`}
                aria-hidden={activeIndex !== index}
              >
                <div className="px-6 pb-6 bg-white">
                  <p className="text-[#********] font-normal text-[18px] leading-relaxed">
                    {item.content}
                  </p>
                </div>
              </div>
              {/* Divider - only show if not the last item */}
              {index < data.length - 1 && (
                <div className="border-b border-gray-200"></div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FAQ;
