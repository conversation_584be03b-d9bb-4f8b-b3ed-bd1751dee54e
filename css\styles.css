/* Global Styles and Base Configuration */

/* Font Family Override */
* {
    font-family: 'Figtree', ui-sans-serif, system-ui, sans-serif !important;
}

/* Base Body Styles */
body {
    width: 100vw;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Figtree', ui-sans-serif, system-ui, sans-serif !important;
}

html,
body {
    height: 100%;
    width: 100%;
}

/* Custom Color Variables */
:root {
    --primary-brown: #563D39;
    --primary-brown-hover: #4a332f;
    --primary-brown-light: #95725D;
    --text-gray: #939393;
    --text-black-light: #00000099;
    --background-light: #563D391A;
    --background-gradient: #0000000D;
    --background-gradient-mid: #0000001A;
    --white: #ffffff;
    --black: #000000;
}

/* Utility Classes */
.input-box-shadow {
    filter: drop-shadow(-4px -4px 44px rgba(0, 0, 0, 0.08));
}

.category:hover,
.icon {
    fill: #015681;
}

/* Custom Button Styles */
.btn-primary-custom {
    font-weight: 900;
    color: white;
    border-radius: 30px;
    border: 1px transparent solid;
    background: var(--primary-brown);
    position: relative;
    transition: all 250ms;
    overflow: hidden;
    text-transform: uppercase;
    font-size: 1rem;
    letter-spacing: .15rem;
    z-index: 1;
}

.btn-primary-custom::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    border-radius: 30px;
    background-color: var(--primary-brown-light);
    z-index: -1;
    transition: all 500ms;
}

.btn-primary-custom:hover {
    color: white;
}

.btn-primary-custom:hover::before {
    width: 100%;
}

.btn-secondary-custom {
    font-weight: 900;
    color: black;
    border-radius: 30px;
    border: 1px transparent solid;
    background: white;
    position: relative;
    transition: all 250ms;
    overflow: hidden;
    text-transform: uppercase;
    font-size: 1rem;
    letter-spacing: .15rem;
    z-index: 1;
}

.btn-secondary-custom::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    border-radius: 30px;
    background-color: #025add;
    z-index: -1;
    transition: all 500ms;
}

.btn-secondary-custom:hover {
    border: 1px #0088cc solid;
    color: white;
}

.btn-secondary-custom:hover::before {
    width: 100%;
}

/* Card Hover Effects */
.card-hover:hover {
    color: white;
}

.card-hover:hover p {
    color: rgba(255, 255, 255, 0.718);
}

.card-hover:hover h3 {
    color: rgba(255, 255, 255, 0.718);
}

.card-hover:hover .about-icon {
    color: white;
}

.card-hover:hover button {
    color: white;
}

.card-hover:hover::before {
    width: 100%;
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(115deg, #f9ce34, #ee2a7b, #6228d7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Custom Swiper Styles */
.custom-swiper .swiper-button-next,
.custom-swiper .swiper-button-prev {
    color: var(--primary-brown);
}

/* Navbar Styles */
.navbar-custom {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1050;
    height: 80px;
}

.navbar-brand img {
    width: 128px;
    height: auto;
}

.navbar-nav-center {
    background: linear-gradient(to right, var(--background-gradient), var(--background-gradient-mid), var(--background-gradient));
    border-radius: 60px;
    padding: 14px 20px;
}

.nav-link-custom {
    color: var(--text-gray) !important;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link-custom:hover,
.nav-link-custom.active {
    color: var(--primary-brown) !important;
    font-weight: 600;
}

.btn-login {
    background-color: var(--primary-brown);
    color: white;
    font-weight: 600;
    border-radius: 6px;
    padding: 6px 20px 6px 20px;
    width: 116px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    transition: background-color 0.3s ease;
}

.btn-login:hover {
    background-color: var(--primary-brown-hover);
    color: white;
}

.btn-signup {
    color: var(--black);
    font-weight: 500;
    transition: color 0.3s ease;
}

.btn-signup:hover {
    color: var(--primary-brown);
}

/* Hero Section Styles */
.hero-section {
    min-height: 100vh;
    padding-top: 80px;
    background-color: white;
}

.hero-title {
    color: var(--primary-brown);
    font-size: 70px;
    line-height: 110%;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    color: var(--text-black-light);
    font-size: 20px;
    line-height: 140%;
    font-weight: 300;
}

/* Responsive Typography */
@media (max-width: 1200px) {
    .hero-title {
        font-size: 50px;
    }
    .hero-subtitle {
        font-size: 18px;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 32px;
    }
    .hero-subtitle {
        font-size: 18px;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 20px;
    }
    .hero-subtitle {
        font-size: 12px;
    }
}

/* Management Features Section */
.management-features {
    background-image: url('../src/assets/Management/Background.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
    padding: 80px 0;
}

.feature-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 20px;
    color: white;
    transition: all 0.3s ease;
    height: 330px;
    max-width: 410px;
}

.feature-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

/* Footer Styles */
.footer-custom {
    background: var(--background-light);
    border-radius: 30px 30px 0 0;
    margin: 0 5%;
    padding: 48px 32px;
}

.footer-link {
    color: #6b7280;
    font-size: 14px;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: var(--primary-brown);
    text-decoration: underline;
}
