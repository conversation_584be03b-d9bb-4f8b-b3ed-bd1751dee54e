// single-blog.js - Handles the dynamic loading of blog content

document.addEventListener('DOMContentLoaded', function() {
  // Get the blog slug from the URL
  const urlParams = new URLSearchParams(window.location.search);
  const blogSlug = urlParams.get('slug');
  
  if (!blogSlug) {
    displayError('Blog post not found. Please try another post.');
    return;
  }
  
  // Load blog data
  loadBlogData(blogSlug);
});

/**
 * Load blog data from API or local storage
 * @param {string} slug - The blog post slug
 */
function loadBlogData(slug) {
  // Show loading state
  showLoadingState();
  
  // Try to get blogs from localStorage first
  const cachedBlogs = localStorage.getItem('flowkar_blogs') || localStorage.getItem('flowkar_explore_blogs');
  
  if (cachedBlogs) {
    try {
      const blogs = JSON.parse(cachedBlogs);
      // Find the blog by matching the slug with the title
      const blog = blogs.find(b => createSlug(b.title) === slug);
      
      if (blog) {
        displayBlogContent(blog);
        loadRelatedBlogs(blog, blogs);
        return;
      }
    } catch (err) {
      console.error('Error parsing cached blogs:', err);
    }
  }
  
  // If not found in cache, fetch from API
  fetchBlogFromAPI(slug);
}

/**
 * Fetch blog data from API
 * @param {string} slug - The blog post slug
 */
function fetchBlogFromAPI(slug) {
  fetch(`https://flowkar.com/api/get-blog-by-id/?id=${slug}`)
    .then(response => {
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.json();
    })
    .then(data => {
      if (data && data.data) {
        displayBlogContent(data.data);
        // Also fetch related blogs
        fetchRelatedBlogs();
      } else {
        displayError('Blog post not found.');
      }
    })
    .catch(error => {
      console.error('Error fetching blog:', error);
      displayError('Failed to load blog post. Please try again later.');
    });
}

/**
 * Display blog content on the page
 * @param {Object} blog - The blog data
 */
function displayBlogContent(blog) {
  // Set page title
  document.title = `${blog.title} - Flowkar`;
  
  // Set hero image
  const heroImage = document.getElementById('blog-hero-image');
  const imageUrl = getBlogImageUrl(blog);
  heroImage.style.backgroundImage = `url(${imageUrl})`;
  heroImage.style.height = '476px';
  heroImage.style.backgroundSize = 'cover';
  heroImage.style.backgroundPosition = 'center';
  
  // Set blog title
  document.getElementById('blog-title').textContent = blog.title;
  
  // Set meta information
  const metaContainer = document.getElementById('blog-meta');
  metaContainer.innerHTML = '';
  
  // Add category badge
  if (blog.category) {
    const categoryBadge = document.createElement('span');
    categoryBadge.className = 'badge bg-secondary rounded-pill px-3 py-2';
    categoryBadge.textContent = blog.category;
    metaContainer.appendChild(categoryBadge);
  }
  
  // Add date badge
  if (blog.date || blog.created_at) {
    const dateBadge = document.createElement('span');
    dateBadge.className = 'badge bg-light text-dark rounded-pill px-3 py-2';
    dateBadge.textContent = blog.date || formatDate(blog.created_at);
    metaContainer.appendChild(dateBadge);
  }
  
  // Add read time badge
  if (blog.readTime) {
    const readTimeBadge = document.createElement('span');
    readTimeBadge.className = 'badge bg-light text-dark rounded-pill px-3 py-2';
    readTimeBadge.textContent = blog.readTime;
    metaContainer.appendChild(readTimeBadge);
  }
  
  // Set blog content
  const blogBody = document.getElementById('blog-body');
  
  // If we have HTML content, use it
  if (blog.content) {
    blogBody.innerHTML = blog.content;
  } 
  // Otherwise, create content from excerpt
  else if (blog.excerpt) {
    blogBody.innerHTML = `<p class="lead">${blog.excerpt}</p>`;
    
    // Add placeholder content if excerpt is short
    if (blog.excerpt.length < 100) {
      blogBody.innerHTML += `
        <p>This is a placeholder for the full blog content. In a real implementation, 
        the complete article would be displayed here.</p>
        <h2>Key Points</h2>
        <ul>
          <li>Important insight about ${blog.category || 'this topic'}</li>
          <li>Valuable information for readers</li>
          <li>Practical tips and advice</li>
        </ul>
        <p>Stay tuned for more updates and insights on our blog!</p>
      `;
    }
  } else {
    blogBody.innerHTML = '<p>No content available for this blog post.</p>';
  }
  
  // Add some styling to the blog content
  blogBody.classList.add('max-w-4xl', 'mx-auto', 'px-4');
}

/**
 * Load related blog posts
 * @param {Object} currentBlog - The current blog post
 * @param {Array} allBlogs - All available blog posts
 */
function loadRelatedBlogs(currentBlog, allBlogs) {
  // Filter out the current blog and get up to 3 related blogs
  const relatedBlogs = allBlogs
    .filter(blog => blog.id !== currentBlog.id)
    .slice(0, 3);
  
  displayRelatedBlogs(relatedBlogs);
}

/**
 * Fetch related blogs from API
 */
function fetchRelatedBlogs() {
  // Try to get blogs from localStorage first
  const cachedBlogs = localStorage.getItem('flowkar_blogs') || localStorage.getItem('flowkar_explore_blogs');
  
  if (cachedBlogs) {
    try {
      const blogs = JSON.parse(cachedBlogs);
      // Get random 3 blogs
      const randomBlogs = getRandomItems(blogs, 3);
      displayRelatedBlogs(randomBlogs);
      return;
    } catch (err) {
      console.error('Error parsing cached blogs for related posts:', err);
    }
  }
  
  // If not in cache, fetch from API
  fetch('https://flowkar.com/api/get-all-blogs/')
    .then(response => {
      if (!response.ok) throw new Error('Network response was not ok');
      return response.json();
    })
    .then(data => {
      if (data && data.data && Array.isArray(data.data)) {
        const randomBlogs = getRandomItems(data.data, 3);
        displayRelatedBlogs(randomBlogs);
      }
    })
    .catch(error => {
      console.error('Error fetching related blogs:', error);
      // Don't show an error to the user, just log it
    });
}

/**
 * Display related blog posts
 * @param {Array} blogs - The related blog posts
 */
function displayRelatedBlogs(blogs) {
  const container = document.getElementById('more-insights-container');
  container.innerHTML = '';
  
  if (!blogs || blogs.length === 0) {
    container.innerHTML = '<div class="col-12 text-center"><p>No related posts found.</p></div>';
    return;
  }
  
  blogs.forEach(blog => {
    const blogCard = document.createElement('div');
    blogCard.className = 'col-md-6 col-lg-4';
    
    const imageUrl = getBlogImageUrl(blog);
    
    blogCard.innerHTML = `
      <div class="card bg-transparent border-0 h-100 blog-card" data-slug="${createSlug(blog.title)}">
        <div class="position-relative mb-3 overflow-hidden rounded-4">
          <img src="${imageUrl}" alt="${blog.title}" class="card-img-top img-fluid" style="height: 200px; object-fit: cover;">
          <div class="position-absolute top-0 end-0 m-2">
            <div class="bg-white bg-opacity-25 rounded-circle p-2 d-flex align-items-center justify-content-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-up-right" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M14 2.5a.5.5 0 0 0-.5-.5h-6a.5.5 0 0 0 0 1h4.793L2.146 13.146a.5.5 0 0 0 .708.708L13 3.707V8.5a.5.5 0 0 0 1 0v-6z"/>
              </svg>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="text-white-50 small">
            ${blog.author || 'Unknown Author'} • ${blog.date || formatDate(blog.created_at) || ''}
          </div>
          <h5 class="card-title mt-2 fw-semibold">${blog.title}</h5>
          <p class="card-text text-white-50 small">${blog.excerpt || ''}</p>
          <div class="d-flex flex-wrap gap-2 mt-2">
            ${blog.tags && Array.isArray(blog.tags) ? blog.tags.map(tag => 
              `<span class="badge rounded-pill bg-white bg-opacity-25 text-white small">${tag}</span>`
            ).join('') : ''}
          </div>
        </div>
      </div>
    `;
    
    container.appendChild(blogCard);
  });
  
  // Add click event listeners to blog cards
  document.querySelectorAll('.blog-card').forEach(card => {
    card.addEventListener('click', function() {
      const slug = this.dataset.slug;
      window.location.href = `single-blog.html?slug=${slug}`;
    });
  });
}

/**
 * Show loading state while content is being fetched
 */
function showLoadingState() {
  document.getElementById('blog-hero-image').innerHTML = 
    '<div class="h-100 w-100 bg-light d-flex align-items-center justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
  
  document.getElementById('blog-title').innerHTML = 
    '<div class="placeholder-glow"><span class="placeholder col-6"></span></div>';
  
  document.getElementById('blog-meta').innerHTML = 
    '<div class="placeholder-glow"><span class="placeholder col-2 me-2"></span><span class="placeholder col-2 me-2"></span><span class="placeholder col-2"></span></div>';
  
  document.getElementById('blog-body').innerHTML = `
    <div class="placeholder-glow">
      <p><span class="placeholder col-12"></span></p>
      <p><span class="placeholder col-10"></span></p>
      <p><span class="placeholder col-8"></span></p>
    </div>
  `;
  
  document.getElementById('more-insights-container').innerHTML = `
    <div class="col-md-6 col-lg-4">
      <div class="placeholder-glow">
        <div class="placeholder col-12" style="height: 200px;"></div>
        <h5 class="placeholder col-8 mt-3"></h5>
        <p class="placeholder col-12"></p>
        <p class="placeholder col-10"></p>
      </div>
    </div>
    <div class="col-md-6 col-lg-4 d-none d-md-block">
      <div class="placeholder-glow">
        <div class="placeholder col-12" style="height: 200px;"></div>
        <h5 class="placeholder col-8 mt-3"></h5>
        <p class="placeholder col-12"></p>
        <p class="placeholder col-10"></p>
      </div>
    </div>
    <div class="col-md-6 col-lg-4 d-none d-lg-block">
      <div class="placeholder-glow">
        <div class="placeholder col-12" style="height: 200px;"></div>
        <h5 class="placeholder col-8 mt-3"></h5>
        <p class="placeholder col-12"></p>
        <p class="placeholder col-10"></p>
      </div>
    </div>
  `;
}

/**
 * Display error message
 * @param {string} message - The error message to display
 */
function displayError(message) {
  document.getElementById('blog-hero-image').style.display = 'none';
  document.getElementById('blog-title').textContent = 'Error';
  document.getElementById('blog-meta').innerHTML = '';
  document.getElementById('blog-body').innerHTML = `<div class="alert alert-danger">${message}</div>`;
  document.getElementById('more-insights-container').innerHTML = '';
}

/**
 * Get the blog image URL
 * @param {Object} blog - The blog data
 * @returns {string} - The image URL
 */
function getBlogImageUrl(blog) {
  if (blog.image) return blog.image;
  
  if (blog.video_thumbnail) {
    return blog.video_thumbnail.startsWith('http') 
      ? blog.video_thumbnail 
      : `https://flowkar.com${blog.video_thumbnail.startsWith('/') ? blog.video_thumbnail : '/' + blog.video_thumbnail}`;
  }
  
  if (blog.banner) {
    return blog.banner.startsWith('http') 
      ? blog.banner 
      : `https://flowkar.com${blog.banner.startsWith('/') ? blog.banner : '/' + blog.banner}`;
  }
  
  // Default image
  return '../src/assets/Blog/Image_10.svg';
}

/**
 * Format date string
 * @param {string} dateString - The date string to format
 * @returns {string} - The formatted date
 */
function formatDate(dateString) {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  } catch (e) {
    return dateString;
  }
}

/**
 * Create a URL-friendly slug from a string
 * @param {string} text - The text to convert to a slug
 * @returns {string} - The slug
 */
function createSlug(text) {
  if (!text) return '';
  
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove non-word chars
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Get random items from an array
 * @param {Array} array - The array to get random items from
 * @param {number} count - The number of items to get
 * @returns {Array} - The random items
 */
function getRandomItems(array, count) {
  if (!array || !Array.isArray(array) || array.length === 0) return [];
  
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}