import React from 'react';
import Image_01 from "../../assets/AboutUs/Mission_01.svg"
import Image_02 from "../../assets/AboutUs/Mission_02.svg"
import Image_03 from "../../assets/AboutUs/Mission_03.svg"
import Image_04 from "../../assets/AboutUs/Mission_04.svg"



const CardImages = [
    Image_01,
    Image_02,
    Image_03,
    Image_04
]

const missionData = [
  {
    img: CardImages[0],
    title: 'Innovation',
    desc: `Flowkar isn’t just adding features - we’re solving real problems creators face every day. Our innovation lies in making complex things simple: from cross-platform scheduling to intelligent content suggestions, we’re always building tools that adapt to the way creators actually work.
We listen to our users, test with real influencers, and stay ahead of platform changes - so you can focus on creating, not catching up.

`,
  },
  {
    img: CardImages[1],
    title: 'Reliability',
    desc: `Creators can’t afford to miss a moment - and neither can we. Flowkar delivers consistent, on-time scheduling, real-time insights, and platform uptime you can count on. Whether you're posting to one audience or millions, you can trust your content will go live exactly how and when you planned.
We believe reliability means clarity too - our dashboards and analytics are clean, understandable, and instantly useful.`,
  },
  {
    img: CardImages[2],
    title: 'Customer Success',
    desc: `Behind Flowkar is a team that understands the grind - because we’ve lived it. Our customer success isn’t just reactive support - it’s proactive partnership. Whether you're launching a new brand or scaling an agency, we work closely to understand your goals and unlock your growth.
From onboarding to optimization, we’re always here to help you win - with real people, not bots.`,
  },
  {
    img:CardImages[3],
    title: 'Performance',
    desc: `Speed and scale matter. Whether you're managing five posts or five hundred, Flowkar is built to perform without slowing you down. Every part of our system — from scheduling to reporting - is optimized for creators who move fast.
Built with modern infrastructure and real-time syncing, you get a platform that grows with you — no lag, no limits, just pure performance.`,
  },
];

const OurMission = () => {
  return (
    <section className="w-[90%] mx-auto py-12 px-4">
      <h2 className="text-[#563D39] text-2xl md:text-3xl font-semibold text-center mb-5">Our Mission is To Make Social Media Effortless</h2>
      <p className="text-center text-[#00000099] mb-8 text-sm md:text-lg font-normal">
        Flowkar is built to make social media simple, smart and effective. From creators to agencies, we help you manage everything in one place. Stay in control, stay connected - across every channel, with your team.
      </p>
      <div className="flex flex-col gap-8">
        {missionData.map((item, idx) => (
          <div key={idx} className="flex flex-col md:flex-row items-center gap-6 md:gap-[60px]">
            <img
              src={item.img}
              alt={item.title}
              className="w-full md:w-[368px] h-[408px] object-cover rounded-[30px]"
            />
            <div className="flex-1">
              <h3 className="text-[#563D39] text-[34px] font-medium mb-[20px]">{item.title}</h3>
              <p className="text-[#00000099] font-light text-sm md:text-lg">{item.desc}</p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default OurMission;
