import React, { useState, useEffect, useRef } from 'react';

const Popup = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    platform_status: [],
    age: '',
    mobile: '',
    description: ''
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const popupRef = useRef(null);

  // Reset form when closed
  useEffect(() => {
    if (!isOpen) {
      setFormData({
        name: '',
        email: '',
        platform_status: [],
        age: '',
        mobile: '',
        description: ''
      });
      setErrors({});
      setSubmitMessage('');
    }
  }, [isOpen]);

  // Handle outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const validateForm = () => {
    const newErrors = {};
    
    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    // Platform status validation
    if (formData.platform_status.length === 0) {
      newErrors.platform_status = 'At least one platform selection is required';
    }
    
    // Age validation - now optional
    if (formData.age && (isNaN(formData.age) || formData.age < 10 || formData.age > 120)) {
      newErrors.age = 'Please enter a valid age between 10 and 120';
    }
    
    // Mobile validation - now optional
    const mobileRegex = /^[0-9]{10}$/;
    if (formData.mobile.trim() && !mobileRegex.test(formData.mobile)) {
      newErrors.mobile = 'Please enter a valid 10-digit mobile number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name === 'platform_status') {
      setFormData(prevData => {
        let updatedPlatforms;
        if (checked) {
          updatedPlatforms = [...prevData.platform_status, value];
        } else {
          updatedPlatforms = prevData.platform_status.filter(platform => platform !== value);
        }
        return {
          ...prevData,
          platform_status: updatedPlatforms
        };
      });
    } else {
      setFormData(prevData => ({
        ...prevData,
        [name]: value
      }));
    }

    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const platformMapping = {
        'Android': 1,
        'Ios': 2,
        'Web': 3
      };

      const platformIds = formData.platform_status.map(platform => 
        platformMapping[platform]
      );

      const modifiedFormData = {
        ...formData,
        platform_status: platformIds
      };

      const response = await fetch('https://api.flowkar.com/api/register-beta-user/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(modifiedFormData),
      });
      
      const api_response = await response.json();
      console.log("Successfully registered", api_response);
      console.log("Successfully message", api_response.message);
      
      if (response.ok) {
        setSubmitMessage(api_response.message);
        
        setFormData({
          name: '',
          email: '',
          platform_status: [],
          age: '',
          mobile: '',
          description: ''
        });
        
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setSubmitMessage(api_response.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      setSubmitMessage('An error occurred during registration');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 font-Ubuntu">
      <div ref={popupRef} className="bg-white rounded-lg shadow-lg w-full max-w-md overflow-y-auto max-h-[90vh]">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <div className="bg-gray-100 p-3 rounded-full mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h2 className="text-xl font-medium text-gray-800">Become a beta user</h2>
            </div>
            <button 
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <form onSubmit={handleSubmit}>
            {/* Name Field */}
            <div className="mb-4">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Your Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className={`w-full px-3 py-2 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-[12px] focus:outline-none focus:ring-2 focus:ring-[#674941]`}
              />
              {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
            </div>
            
            {/* Email Field */}
            <div className="mb-4">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full px-3 py-2 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-[12px] focus:outline-none focus:ring-2 focus:ring-[#674941]`}
              />
              {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
            </div>
            
            {/* Platform Status Field */}
            <div className="mb-4">
              <p className="block text-sm font-medium text-gray-700 mb-2">
                User Type <span className="text-red-500">*</span>
              </p>
              <div className="flex gap-4">
                {['Android', 'Ios', 'Web'].map(platform => (
                  <label key={platform} className="flex items-center">
                    <input
                      type="checkbox"
                      name="platform_status"
                      value={platform}
                      checked={formData.platform_status.includes(platform)}
                      onChange={handleChange}
                      className="h-4 w-4 text-gray-600 accent-[#674941]"
                    />
                    <span className="ml-2 text-sm text-gray-700">{platform}</span>
                  </label>
                ))}
              </div>
              {errors.platform_status && <p className="mt-1 text-sm text-red-500">{errors.platform_status}</p>}
            </div>
            
            {/* Age Field - now optional */}
            <div className="mb-4">
              <label htmlFor="age" className="block text-sm font-medium text-gray-700 mb-2">
                Age <span className="text-xs text-gray-500">(Optional)</span>
              </label>
              <input
                type="number"
                id="age"
                name="age"
                value={formData.age}
                onChange={handleChange}
                min="10"
                max="120"
                className={`w-full px-3 py-2 border ${errors.age ? 'border-red-500' : 'border-gray-300'} rounded-[12px] focus:outline-none focus:ring-2 focus:ring-[#674941]`}
              />
              {errors.age && <p className="mt-1 text-sm text-red-500">{errors.age}</p>}
            </div>
            
            {/* Mobile Field - now optional */}
            <div className="mb-4">
              <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 mb-2">
                Mobile Number <span className="text-xs text-gray-500">(Optional)</span>
              </label>
              <input
                type="tel"
                id="mobile"
                name="mobile"
                value={formData.mobile}
                onChange={handleChange}
                maxLength="10"
                className={`w-full px-3 py-2 border ${errors.mobile ? 'border-red-500' : 'border-gray-300'} rounded-[12px] focus:outline-none focus:ring-2 focus:ring-[#674941]`}
              />
              {errors.mobile && <p className="mt-1 text-sm text-red-500">{errors.mobile}</p>}
            </div>

            {/* Feedback Field (Optional) */}
            <div className="mb-6">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Tell us something about the app <span className="text-xs text-gray-500">(Optional)</span>
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows="4"
                className="w-full px-3 py-2 border border-gray-300 rounded-[12px] focus:outline-none focus:ring-2 focus:ring-[#674941]"
              ></textarea>
            </div>
            
            {/* Submit message */}
            {submitMessage && (
              <div className={`mb-4 text-center text-sm font-medium text-black ${submitMessage}`}>
                {submitMessage}
              </div>
            )}
            
            {/* Submit button */}
            <div className="flex justify-center">
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-[158px] h-[58px] bg-[#674941] text-white py-2 px-4 rounded-[8px] focus:outline-none focus:ring-2 focus:ring-[#674941] focus:ring-offset-2 transition-colors duration-300 text-center disabled:opacity-70"
              >
                {isSubmitting ? 'Submitting...' : 'Submit'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Popup;