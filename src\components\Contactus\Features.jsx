import React, { useState, useEffect } from "react";

const features = [
  {
    title: "Support That Puts You First",
    description:
      "At Flowkar, we're committed to making your journey smooth and rewarding. Whether you need help with features, face a technical hiccup, or just want guidance – our team is always here to support you at every step.",
  },
  {
    title: "Driven by Innovation",
    description:
      "At Flowkar, we don't just follow trends — we set them. Our forward-thinking approach ensures you're always equipped with cutting-edge tools designed to elevate and simplify your social media experience.",
  },
  {
    title: "Committed to Excellence",
    description:
      "We build Flowkar with precision and care, ensuring top-tier performance and dependable service. Your experience matters most, and we are dedicated to making every interaction outstanding.",
  },
];

// Custom skeleton for Features section
const FeaturesSkeleton = () => (
  <section className="flex justify-center  py-12 sm:py-16 md:py-20 lg:py-[120px] font-sans animate-pulse">
    <div className="bg-[#563D39] text-white px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-6 sm:py-8 md:py-10 lg:py-12 rounded-2xl sm:rounded-3xl lg:rounded-[40px] w-full max-w-7xl">
      <div className="h-6 sm:h-8 md:h-10 bg-white bg-opacity-20 rounded w-full sm:w-3/4 md:w-2/3 mx-auto mb-6 sm:mb-8 md:mb-10 lg:mb-12" />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8 lg:gap-12">
        {[...Array(3)].map((_, idx) => (
          <div
            key={idx}
            className="bg-white/10 rounded-xl sm:rounded-2xl lg:rounded-[18px] p-4 sm:p-6 md:p-8 lg:p-[30px] flex flex-col"
          >
            <div className="h-5 sm:h-6 md:h-7 lg:h-8 bg-white bg-opacity-30 rounded w-3/4 sm:w-2/3 md:w-1/2 mb-3 sm:mb-4 md:mb-5 lg:mb-6" />
            <div className="h-3 sm:h-4 bg-white bg-opacity-20 rounded w-full mb-2" />
            <div className="h-3 sm:h-4 bg-white bg-opacity-20 rounded w-5/6 mb-2" />
            <div className="h-3 sm:h-4 bg-white bg-opacity-20 rounded w-3/4" />
          </div>
        ))}
      </div>
    </div>
  </section>
);

const FeatureCard = ({ title, description }) => (
  <div className="bg-white/5 rounded-xl sm:rounded-2xl lg:rounded-[18px] p-4 sm:p-6 md:p-8 lg:p-[30px] text-left h-full flex flex-col">
    <h3 className="text-lg sm:text-xl md:text-xl lg:text-xl font-semibold mb-3 sm:mb-4 md:mb-5 lg:mb-6 text-white leading-tight">
      {title}
    </h3>
    <p className="text-sm sm:text-base md:text-base lg:text-base leading-relaxed sm:leading-6 text-[#FFFFFF] font-extralight flex-grow">
      {description}
    </p>
  </div>
);

const FeatureSection = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return <FeaturesSkeleton />;
  }

  return (
    <section className="flex justify-center  py-12 sm:py-16 md:py-20 lg:py-[120px] font-figtree">
      <div className="bg-[#563D39] text-white px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-6 sm:py-8 md:py-10 lg:py-12 rounded-2xl sm:rounded-3xl lg:rounded-[40px] w-[90%]">
        <h2 className="text-center text-xl sm:text-2xl md:text-3xl lg:text-3xl font-light mb-8 sm:mb-10 md:mb-12 lg:mb-16 leading-relaxed tracking-wide px-2 sm:px-4">
          "Support, Innovation, And Excellence - All In One"
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8 lg:gap-12">
          {features.map((feature, idx) => (
            <FeatureCard key={idx} {...feature} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureSection;
