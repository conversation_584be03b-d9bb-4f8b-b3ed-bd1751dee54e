/**
 * Asset Preloader Utility
 * Implements intelligent asset caching and preloading strategies
 */

class AssetPreloader {
  constructor() {
    this.cache = new Map();
    this.loadingPromises = new Map();
    this.preloadQueue = [];
    this.isPreloading = false;
    this.maxCacheSize = 50; // Maximum number of cached assets
    this.criticalAssets = new Set();

    // Initialize performance observer if available
    this.initPerformanceObserver();
  }

  /**
   * Initialize performance observer to track asset loading
   */
  initPerformanceObserver() {
    if ("PerformanceObserver" in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (
              entry.initiatorType === "img" ||
              entry.initiatorType === "css"
            ) {
              console.log(`Asset loaded: ${entry.name} in ${entry.duration}ms`);
            }
          });
        });
        observer.observe({ entryTypes: ["resource"] });
      } catch (error) {
        console.warn("Performance observer not supported:", error);
      }
    }
  }

  /**
   * Preload critical assets immediately
   */
  async preloadCriticalAssets(assets) {
    const criticalPromises = assets.map((asset) => {
      this.criticalAssets.add(asset);
      return this.preloadAsset(asset, true);
    });

    try {
      await Promise.allSettled(criticalPromises);
      console.log("Critical assets preloaded");
    } catch (error) {
      console.warn("Some critical assets failed to preload:", error);
    }
  }

  /**
   * Preload a single asset with caching
   */
  async preloadAsset(src, priority = false) {
    // Return cached asset if available
    if (this.cache.has(src)) {
      return this.cache.get(src);
    }

    // Return existing loading promise if asset is already being loaded
    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src);
    }

    // Create loading promise
    const loadingPromise = this.loadAsset(src, priority);
    this.loadingPromises.set(src, loadingPromise);

    try {
      const result = await loadingPromise;
      this.cache.set(src, result);
      this.loadingPromises.delete(src);

      // Manage cache size
      this.manageCacheSize();

      return result;
    } catch (error) {
      this.loadingPromises.delete(src);
      throw error;
    }
  }

  /**
   * Load asset using appropriate method
   */
  loadAsset(src, priority = false) {
    console.log(
      `AssetPreloader: Loading asset ${src} with priority: ${priority}`
    );
    return new Promise((resolve, reject) => {
      const img = new Image();

      // Set loading priority
      if (priority) {
        img.loading = "eager";
        img.fetchPriority = "high";
      } else {
        img.loading = "lazy";
        img.fetchPriority = "low";
      }

      img.onload = () => {
        console.log(`AssetPreloader: Successfully loaded ${src}`);
        resolve({
          src,
          element: img,
          loadTime: Date.now(),
          cached: true,
        });
      };

      img.onerror = (error) => {
        console.error(`AssetPreloader: Failed to load ${src}`, error);
        reject(new Error(`Failed to load asset: ${src}`));
      };

      img.src = src;
    });
  }

  /**
   * Manage cache size by removing oldest non-critical assets
   */
  manageCacheSize() {
    if (this.cache.size <= this.maxCacheSize) return;

    const entries = Array.from(this.cache.entries());
    const nonCriticalEntries = entries.filter(
      ([src]) => !this.criticalAssets.has(src)
    );

    // Sort by load time (oldest first)
    nonCriticalEntries.sort((a, b) => a[1].loadTime - b[1].loadTime);

    // Remove oldest entries
    const toRemove = this.cache.size - this.maxCacheSize;
    for (let i = 0; i < toRemove && i < nonCriticalEntries.length; i++) {
      this.cache.delete(nonCriticalEntries[i][0]);
    }
  }

  /**
   * Batch preload assets with intelligent scheduling
   */
  async batchPreload(assets, options = {}) {
    const { batchSize = 3, delay = 100, priority = false } = options;

    for (let i = 0; i < assets.length; i += batchSize) {
      const batch = assets.slice(i, i + batchSize);
      const batchPromises = batch.map((asset) =>
        this.preloadAsset(asset, priority).catch((error) => {
          console.warn(`Failed to preload ${asset}:`, error);
          return null;
        })
      );

      await Promise.allSettled(batchPromises);

      // Add delay between batches to prevent overwhelming the browser
      if (i + batchSize < assets.length) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Get cached asset or load if not cached
   */
  async getAsset(src) {
    if (this.cache.has(src)) {
      return this.cache.get(src);
    }

    return this.preloadAsset(src);
  }

  /**
   * Preload assets when browser is idle
   */
  preloadOnIdle(assets) {
    if ("requestIdleCallback" in window) {
      requestIdleCallback(
        () => {
          this.batchPreload(assets, { priority: false, delay: 50 });
        },
        { timeout: 5000 }
      );
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => {
        this.batchPreload(assets, { priority: false, delay: 100 });
      }, 1000);
    }
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
    this.loadingPromises.clear();
    this.criticalAssets.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      cacheSize: this.cache.size,
      maxCacheSize: this.maxCacheSize,
      criticalAssets: this.criticalAssets.size,
      loadingAssets: this.loadingPromises.size,
    };
  }
}

// Create singleton instance
const assetPreloader = new AssetPreloader();

export default assetPreloader;

/**
 * React hook for asset preloading
 */
export const useAssetPreloader = () => {
  return {
    preloadAsset: (src, priority) => assetPreloader.preloadAsset(src, priority),
    preloadCritical: (assets) => assetPreloader.preloadCriticalAssets(assets),
    batchPreload: (assets, options) =>
      assetPreloader.batchPreload(assets, options),
    preloadOnIdle: (assets) => assetPreloader.preloadOnIdle(assets),
    getAsset: (src) => assetPreloader.getAsset(src),
    getCacheStats: () => assetPreloader.getCacheStats(),
    clearCache: () => assetPreloader.clearCache(),
  };
};

/**
 * Utility function to extract asset URLs from components
 */
export const extractAssetUrls = (assets) => {
  return Object.values(assets).filter(
    (asset) =>
      typeof asset === "string" &&
      (asset.endsWith(".svg") ||
        asset.endsWith(".png") ||
        asset.endsWith(".jpg") ||
        asset.endsWith(".jpeg") ||
        asset.endsWith(".webp"))
  );
};
