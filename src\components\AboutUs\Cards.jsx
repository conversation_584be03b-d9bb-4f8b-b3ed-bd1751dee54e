import React from "react";
import Card01 from "../../assets/AboutUs/Card_01.svg";
import Card02 from "../../assets/AboutUs/Card_02.svg";
import Card03 from "../../assets/AboutUs/Card_03.svg";
import Card04 from "../../assets/AboutUs/Card_04.svg";

const cards = [
  {
    icon: (
      <img src={Card02} alt="Our Vision" className="w-[61px] h-[60px] mb-2" />
    ),
    title: "Our Vision",
    desc: "Reimagining social media-one dashboard, every platform, full control.",
  },
  {
    icon: (
      <img src={Card01} alt="Our Mission" className="w-[61px] h-[60px] mb-2" />
    ),
    title: "Our Mission",
    desc: "Unifying platforms. Simplifying chaos. Powering creators without the clutter.",
  },
  {
    icon: (
      <img src={Card03} alt="Our Team" className="w-[61px] h-[60px] mb-2" />
    ),
    title: "Our Team",
    desc: "Thinkers, builders, dreamers—engineering simplicity for the social world.",
  },
  {
    icon: (
      <img src={Card04} alt="Our Story" className="w-[61px] h-[60px] mb-2" />
    ),
    title: "Our Story",
    desc: "From chaos came clarity. Flowkar was our turning point.",
  },
];

const Cards = () => (
  <div className="w-full flex justify-center">
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 w-[90%] my-[120px] px-4 sm:px-6 lg:px-8">
      {cards.map((card, idx) => (
        <div
          key={idx}
          className="flex flex-col items-center bg-white rounded-2xl px-6 py-8 sm:px-6 sm:py-8 md:px-8 md:py-8 lg:px-[30px] lg:py-[20px] w-full h-full text-center border border-[#E3E3E3] shadow-sm"
        >
          <div className="mb-4">{card.icon}</div>
          <h3 className="text-lg sm:text-xl font-semibold text-[#563D39] mb-2">
            {card.title}
          </h3>
          <p className="text-[#00000099] text-sm sm:text-base opacity-80 leading-relaxed font-normal">
            {card.desc}
          </p>
        </div>
      ))}
    </div>
  </div>
);

export default Cards;
