import React, { useEffect } from "react";

function RedirectingLinks() {
  useEffect(() => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;

    // iOS detection
    if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
      window.location.href = "https://apps.apple.com/in/app/flowkar/id6740058663";
    }
    // Android detection
    else if (/android/i.test(userAgent)) {
      window.location.href =
        "https://play.google.com/store/apps/details?id=com.app.flowkar";
    }
    // Default to a website or fallback
    else {
      window.location.href = "https://flowkar.com";
    }
  }, []);

  return (
    <div>
      <p>Hang tight! We're redirecting you to the right place...</p>
    </div>
  );
}

export default RedirectingLinks;
