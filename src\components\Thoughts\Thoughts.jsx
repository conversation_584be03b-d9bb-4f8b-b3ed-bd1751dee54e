  import Dummy_01 from "../../assets/Thoughts/Dummy_01.svg";
  import Dummy_02 from "../../assets/Thoughts/Dummy_02.svg";
  import Dummy_03 from "../../assets/Thoughts/Dummy_03.svg";
  import Dummy_04 from "../../assets/Thoughts/Dummy_04.svg";
  import Dummy_05 from "../../assets/Thoughts/Dummy_05.svg";
  import Dummy_06 from "../../assets/Thoughts/Dummy_06.svg";
  import Dummy_07 from "../../assets/Thoughts/Dummy_07.svg";
  import PlayButton from "../../assets/Thoughts/PlayButton.svg";
  import React, { useEffect, useState } from "react";
  import { Navigate, useNavigate } from "react-router-dom";
  import { createSlug } from "../../utils/slugUtils";
  import RightArrow from "../../assets/AboutUs/RightArrow.svg";

  function Thoughts() {
    const [blogs, setBlogs] = useState([]);
    const [loading, setLoading] = useState(true);
    const navigate = useNavigate();

    useEffect(() => {
      async function fetchBlogs() {
        setLoading(true);
        try {
          const response = await fetch("https://flowkar.com/api/get-all-blogs/");
          const data = await response.json();
          const mappedBlogs = (data?.data || []).map((item) => ({
            id: item.id,
            title: item.title || "Untitled Blog",
            subtitle: item.category || "General",
            description: item.keywords || "",
            image: item.video_thumbnail
              ? item.video_thumbnail.startsWith("http")
                ? item.video_thumbnail
                : `https://flowkar.com${
                    item.video_thumbnail.startsWith("/")
                      ? item.video_thumbnail
                      : "/" + item.video_thumbnail
                  }`
              : item.banner
              ? item.banner.startsWith("http")
                ? item.banner
                : `https://flowkar.com${
                    item.banner.startsWith("/") ? item.banner : "/" + item.banner
                  }`
              : Dummy_01, // fallback to Dummy_01 if no image
          }));
          setBlogs(mappedBlogs.slice(0, 7));
        } catch (e) {
          setBlogs([]);
        } finally {
          setLoading(false);
        }
      }
      fetchBlogs();
    }, []);

    // Fallback to static data if API fails or loading
    const thoughtsData =
      blogs.length === 7 && !loading
        ? blogs
        : [
            {
              id: 1,
              title:
                "A Day in the Life of an Advanced Humanity's Open Window to Space",
              subtitle:
                "Artificial Intelligence with the Intention of freedom related to space and time",
              description:
                "Exploring the vast cosmos with the freedom of thought and the intention to expand our understanding of the universe.",
              image: Dummy_01,
              size: "large",
              hasPlayButton: false,
            },
            {
              id: 2,
              title: "Echoes Through The Land",
              image: Dummy_03,
              size: "medium",
              hasPlayButton: false,
            },
            {
              id: 3,
              title: "Nora Robson",
              image: Dummy_04,
              size: "small",
              hasPlayButton: false,
            },
            {
              id: 4,
              title: "The Great Gatsby",
              image: Dummy_05,
              size: "small",
              hasPlayButton: false,
            },
            {
              id: 5,
              title: "Spiritual Whispers",
              image: Dummy_02,
              size: "medium-bottom",
              hasPlayButton: false,
            },
            {
              id: 6,
              title: "Ambergris",
              image: Dummy_06,
              size: "medium-bottom",
              hasPlayButton: false,
            },
            {
              id: 7,
              title: "Culture King",
              image: Dummy_07,
              size: "medium-bottom",
              hasPlayButton: false,
            },
          ];

    const PlayButton = () => (
      <div className="absolute inset-0 flex items-center justify-center z-10">
        <div className="h-[92px] w-[92px] border-[1px] border-[#FFFFFF66] rounded-full flex items-center justify-center">
          <div className="h-[76px] w-[76px] rounded-full p-[8px] bg-[#FFFFFF3D] flex items-center justify-center">
            <svg
              className="w-[40px] h-[40px] text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M8 5v14l11-7z" />
            </svg>
          </div>
        </div>
      </div>
    );

    const handleBlogClick = (blog) => {
      if (blog && blog.title) {
        const blogSlug = createSlug(blog.title);
        navigate(`/blog/${blogSlug}`);
      }
    };

    // Skeleton Loader for grid
    const SkeletonCard = ({ hasPlayButton = false }) => (
      <div
        className={
          "relative border-[1px] border-[#FFFFFF] rounded-[20px] overflow-hidden bg-[#6d4c46] animate-pulse w-full h-full flex flex-col justify-end"
        }
      >
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent rounded-[20px]" />
        <div className="p-4">
          <div className="h-4 bg-white/30 rounded w-3/4 mb-2" />
          <div className="h-3 bg-white/20 rounded w-1/2 mb-1" />
          <div className="h-3 bg-white/10 rounded w-full" />
        </div>
      </div>
    );

    return (
      <div className="w-[90%] mx-auto bg-[#563D39] py-16 px-4 rounded-[40px] pt-[60px] pb-[40px]">
        <div className="w-[95%] mx-auto">
          {/* Header */}
          <div className="flex flex-col md:flex-row justify-between items-center text-center mb-8 md:mb-12 gap-4 md:gap-0">
            <h2 className="text-white text-[24px] sm:text-[28px] md:text-[36px] lg:text-[40px] font-semibold mb-2 md:mb-4 order-1 md:order-1">
              Thoughts That Move You.
            </h2>
            <p className="text-[#AA8882] font-semibold text-[18px] sm:text-[22px] md:text-[28px] lg:text-[34px] order-2 md:order-2">
              Stories that stay with you
            </p>
          </div>

          {/* Main Grid Layout - Clean 6x4 Grid */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-6 md:grid-rows-4 gap-4 h-auto md:h-[800px]">
              {/* Large Card - Top Left (2 columns, 3 rows) */}
              <div className="md:col-span-2 md:row-span-3 h-[500px] md:h-auto rounded-[20px] flex flex-col">
                <SkeletonCard />
              </div>
              {/* Top Right - Large Landscape Card (4 columns, 2 rows) */}
              <div className="md:col-span-4 md:row-span-2 h-[350px] md:h-auto">
                <SkeletonCard hasPlayButton={true} />
              </div>
              {/* Small Card 1 - Middle Right (2 columns, 1 row) */}
              <div className="md:col-span-2 md:row-span-1 h-[200px] md:h-auto">
                <SkeletonCard />
              </div>
              {/* Small Card 2 - Middle Right (2 columns, 1 row) */}
              <div className="md:col-span-2 md:row-span-1 h-[200px] md:h-auto">
                <SkeletonCard />
              </div>
              {/* Bottom Left Card (2 columns, 1 row) */}
              <div className="md:col-span-2 md:row-span-1 h-[200px] md:h-auto">
                <SkeletonCard hasPlayButton={true} />
              </div>
              {/* Bottom Card 1 (2 columns, 1 row) */}
              <div className="md:col-span-2 md:row-span-1 h-[200px] md:h-auto">
                <SkeletonCard />
              </div>
              {/* Bottom Card 2 (2 columns, 1 row) */}
              <div className="md:col-span-2 md:row-span-1 h-[200px] md:h-auto">
                <SkeletonCard />
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-6 md:grid-rows-4 gap-4 h-auto md:h-[800px]">
              {/* Large Card - Top Left (2 columns, 3 rows) */}
              <div className="md:col-span-2 md:row-span-3 h-[500px] md:h-auto  rounded-[20px] flex flex-col">
                <div
                  className="relative flex-1 rounded-t-[20px] overflow-hidden group cursor-pointer"
                  onClick={() => handleBlogClick(thoughtsData[0])}
                >
                  <img
                    src={thoughtsData[0].image}
                    alt={thoughtsData[0].title}
                    className="w-full h-full object-cover transition-transform duration-500 rounded-[20px] border-[2px] border-[#FFFFFF]"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent rounded-[20px]"></div>
                </div>
                <div className="py-4 md:py-6 text-white bg-[#563D39] rounded-b-[20px]">
                  <h3 className="text-base sm:text-lg md:text-xl font-medium mb-2 leading-tight">
                    {thoughtsData[0].title}
                  </h3>
                  <p className="text-xs sm:text-sm text-white/90 mb-1">
                    {thoughtsData[0].subtitle}
                  </p>
                  <p className="text-xs sm:text-sm text-white/80 leading-relaxed">
                    {thoughtsData[0].description}
                  </p>
                </div>
              </div>

              {/* Top Right - Large Landscape Card (4 columns, 2 rows) */}
              <div className="md:col-span-4 md:row-span-2 h-[350px] md:h-auto">
                <div
                  className="relative h-full rounded-[20px] border-[1px] border-[#FFFFFF] overflow-hidden group cursor-pointer"
                  onClick={() => handleBlogClick(thoughtsData[1])}
                >
                  <img
                    src={thoughtsData[1].image}
                    alt={thoughtsData[1].title}
                    className="w-full h-full object-cover transition-transform duration-500 rounded-[20px]"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  {thoughtsData[1].hasPlayButton && <PlayButton />}
                  <div className="absolute bottom-0 left-0 right-0 p-3 md:p-4 text-white">
                    <h3 className="text-sm sm:text-base md:text-lg font-bold">
                      {thoughtsData[1].title}
                    </h3>
                  </div>
                </div>
              </div>

              {/* Small Card 1 - Middle Right (2 columns, 1 row) */}
              <div className="md:col-span-2 md:row-span-1 h-[200px] md:h-auto">
                <div
                  className="relative h-full border-[1px] border-[#FFFFFF] rounded-[20px] overflow-hidden group cursor-pointer"
                  onClick={() => handleBlogClick(thoughtsData[2])}
                >
                  <img
                    src={thoughtsData[2].image}
                    alt={thoughtsData[2].title}
                    className="w-full h-full object-cover transition-transform duration-500 rounded-[20px]"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-2 md:p-3 text-white">
                    <h3 className="text-xs sm:text-sm md:text-base font-bold">
                      {thoughtsData[2].title}
                    </h3>
                  </div>
                </div>
              </div>

              {/* Small Card 2 - Middle Right (2 columns, 1 row) */}
              <div className="md:col-span-2 md:row-span-1 h-[200px] md:h-auto">
                <div
                  className="relative h-full border-[1px] border-[#FFFFFF] rounded-[20px] overflow-hidden group cursor-pointer"
                  onClick={() => handleBlogClick(thoughtsData[3])}
                >
                  <img
                    src={thoughtsData[3].image}
                    alt={thoughtsData[3].title}
                    className="w-full h-full object-cover transition-transform duration-500 rounded-[20px]"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-2 md:p-3 text-white">
                    <h3 className="text-xs sm:text-sm md:text-base font-bold">
                      {thoughtsData[3].title}
                    </h3>
                  </div>
                </div>
              </div>

              {/* Bottom Left Card (2 columns, 1 row) */}
              <div className="md:col-span-2 md:row-span-1 h-[200px] md:h-auto">
                <div
                  className="relative h-full border-[1px] border-[#FFFFFF] rounded-[20px] overflow-hidden group cursor-pointer"
                  onClick={() => handleBlogClick(thoughtsData[4])}
                >
                  <img
                    src={thoughtsData[4].image}
                    alt={thoughtsData[4].title}
                    className="w-full h-full object-cover transition-transform duration-500 rounded-[20px]"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  {thoughtsData[4].hasPlayButton && <PlayButton />}
                  <div className="absolute bottom-0 left-0 right-0 p-3 md:p-4 text-white">
                    <h3 className="text-sm sm:text-base md:text-lg font-bold">
                      {thoughtsData[4].title}
                    </h3>
                  </div>
                </div>
              </div>

              {/* Bottom Card 1 (2 columns, 1 row) */}
              <div className="md:col-span-2 md:row-span-1 h-[200px] md:h-auto">
                <div
                  className="relative h-full border-[1px] border-[#FFFFFF] rounded-[20px] overflow-hidden group cursor-pointer"
                  onClick={() => handleBlogClick(thoughtsData[5])}
                >
                  <img
                    src={thoughtsData[5].image}
                    alt={thoughtsData[5].title}
                    className="w-full h-full object-cover transition-transform duration-500 rounded-[20px]"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-3 md:p-4 text-white">
                    <h3 className="text-sm sm:text-base md:text-lg font-bold">
                      {thoughtsData[5].title}
                    </h3>
                  </div>
                </div>
              </div>

              {/* Bottom Card 2 (2 columns, 1 row) */}
              <div className="md:col-span-2 md:row-span-1 h-[200px] md:h-auto">
                <div
                  className="relative h-full border-[1px] border-[#FFFFFF] rounded-[20px] overflow-hidden group cursor-pointer"
                  onClick={() => handleBlogClick(thoughtsData[6])}
                >
                  <img
                    src={thoughtsData[6].image}
                    alt={thoughtsData[6].title}
                    className="w-full h-full object-cover transition-transform duration-500 rounded-[20px]"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-3 md:p-4 text-white">
                    <h3 className="text-sm sm:text-base md:text-lg font-bold">
                      {thoughtsData[6].title}
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        {/* View All Button - Bottom Right */}
        <div className="w-full flex justify-center mt-8">
          <button
            onClick={() => navigate("/blogs")}
            className="bg-white text-black px-4 py-2 rounded-[6px] transition-colors flex items-center justify-around space-x-2 w-[150px] h-[42px] pt-[6px] pr-[6px] pb-[6px] pl-[20px] mt-[20px] font-normal"
          >
            <span>View All</span>
            <img
              src={RightArrow}
              alt="Right Arrow"
              className="w-[30px] h-[30px]"

            />
          </button>
        </div>
      </div>
    );
  }

  export default Thoughts;
