// Animation utilities and handlers for Flowkar website

// Animation configuration
const animationConfig = {
    duration: {
        fast: 300,
        normal: 600,
        slow: 1000
    },
    easing: {
        easeOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
        easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
    },
    threshold: 0.1
};

// Intersection Observer for scroll animations
let scrollObserver;

// Initialize all animations
function initializeAllAnimations() {
    initializeScrollAnimations();
    initializeHoverAnimations();
    initializeLoadingAnimations();
    initializeParallaxEffects();
    console.log('All animations initialized');
}

// Initialize scroll-based animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: animationConfig.threshold,
        rootMargin: '0px 0px -50px 0px'
    };
    
    scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateElement(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe elements with animation classes
    const animatedElements = document.querySelectorAll('.animate-on-scroll, .fade-in, .slide-in-left, .slide-in-right, .scale-in');
    animatedElements.forEach(el => {
        scrollObserver.observe(el);
    });
}

// Animate individual element
function animateElement(element) {
    if (element.classList.contains('animate-on-scroll')) {
        element.classList.add('in-view');
    }
    
    if (element.classList.contains('fade-in')) {
        element.style.animation = 'fadeIn 0.6s ease-out forwards';
    }
    
    if (element.classList.contains('slide-in-left')) {
        element.style.animation = 'slideInLeft 0.8s ease-out forwards';
    }
    
    if (element.classList.contains('slide-in-right')) {
        element.style.animation = 'slideInRight 0.8s ease-out forwards';
    }
    
    if (element.classList.contains('scale-in')) {
        element.style.animation = 'scaleIn 0.5s ease-out forwards';
    }
}

// Initialize hover animations
function initializeHoverAnimations() {
    // Card hover effects
    const cards = document.querySelectorAll('.card-hover, .feature-card, .testimonial-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'all 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Button hover effects
    const buttons = document.querySelectorAll('.btn-primary-custom, .btn-secondary-custom');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'all 0.2s ease';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // Social icon hover effects
    const socialIcons = document.querySelectorAll('.social-icon');
    socialIcons.forEach(icon => {
        icon.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.1)';
            this.style.transition = 'all 0.3s ease';
        });
        
        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Initialize loading animations
function initializeLoadingAnimations() {
    // Image loading with fade-in effect
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        if (!img.complete) {
            img.style.opacity = '0';
            img.addEventListener('load', function() {
                this.style.transition = 'opacity 0.3s ease';
                this.style.opacity = '1';
            });
        }
    });
    
    // Skeleton loading for dynamic content
    const skeletonElements = document.querySelectorAll('.skeleton');
    skeletonElements.forEach(skeleton => {
        setTimeout(() => {
            skeleton.classList.remove('skeleton');
            skeleton.style.opacity = '1';
        }, Math.random() * 1000 + 500); // Random delay between 500-1500ms
    });
}

// Initialize parallax effects
function initializeParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.parallax');
    
    if (parallaxElements.length > 0) {
        window.addEventListener('scroll', debounce(() => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            parallaxElements.forEach(element => {
                element.style.transform = `translateY(${rate}px)`;
            });
        }, 10));
    }
}

// Gallery animation functions
function startGalleryAnimations() {
    // Mobile gallery animations
    const mobileRows = document.querySelectorAll('.animate-scroll-left, .animate-scroll-right');
    mobileRows.forEach(row => {
        row.style.animationPlayState = 'running';
    });
    
    // Desktop gallery animations
    const desktopColumns = document.querySelectorAll('.animate-scroll-up, .animate-scroll-down');
    desktopColumns.forEach(column => {
        column.style.animationPlayState = 'running';
    });
}

function pauseGalleryAnimations() {
    const animatedElements = document.querySelectorAll('.animate-scroll-left, .animate-scroll-right, .animate-scroll-up, .animate-scroll-down');
    animatedElements.forEach(element => {
        element.style.animationPlayState = 'paused';
    });
}

// Performance optimization for animations
function optimizeAnimations() {
    // Reduce animations on low-end devices
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
        document.documentElement.style.setProperty('--animation-duration', '0.3s');
    }
    
    // Disable animations if user prefers reduced motion
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        disableAnimations();
    }
    
    // Pause animations when tab is not visible
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            pauseGalleryAnimations();
        } else {
            startGalleryAnimations();
        }
    });
}

// Disable animations for accessibility
function disableAnimations() {
    const style = document.createElement('style');
    style.textContent = `
        *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    `;
    document.head.appendChild(style);
}

// Smooth scroll with easing
function smoothScrollTo(target, duration = 1000) {
    const targetElement = typeof target === 'string' ? document.querySelector(target) : target;
    if (!targetElement) return;
    
    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - 100;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;
    
    function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);
        if (timeElapsed < duration) requestAnimationFrame(animation);
    }
    
    function ease(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }
    
    requestAnimationFrame(animation);
}

// Stagger animations for multiple elements
function staggerAnimation(elements, delay = 100) {
    elements.forEach((element, index) => {
        setTimeout(() => {
            element.classList.add('animate');
        }, index * delay);
    });
}

// Counter animation
function animateCounter(element, start, end, duration = 2000) {
    const range = end - start;
    const increment = range / (duration / 16);
    let current = start;
    
    const timer = setInterval(() => {
        current += increment;
        element.textContent = Math.floor(current);
        
        if (current >= end) {
            element.textContent = end;
            clearInterval(timer);
        }
    }, 16);
}

// Typewriter effect
function typewriterEffect(element, text, speed = 50) {
    element.textContent = '';
    let i = 0;
    
    const timer = setInterval(() => {
        element.textContent += text.charAt(i);
        i++;
        
        if (i >= text.length) {
            clearInterval(timer);
        }
    }, speed);
}

// Utility function to debounce events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize animations when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeAllAnimations();
    optimizeAnimations();
    
    // Start gallery animations after a short delay
    setTimeout(() => {
        startGalleryAnimations();
    }, 500);
});

// Export functions for use in other scripts
window.AnimationUtils = {
    smoothScrollTo,
    staggerAnimation,
    animateCounter,
    typewriterEffect,
    startGalleryAnimations,
    pauseGalleryAnimations
};
