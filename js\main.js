// Main JavaScript file for Flowkar website

// Global variables
let currentPage = 'home';
let isLoading = false;

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize the application
function initializeApp() {
    console.log('Initializing Flowkar website...');
    
    // Load components
    loadNavbar();
    loadHeroSection();
    loadManagementFeatures();
    loadSocialMediaIcons();
    loadInfoCard();
    loadAboutUsHero();
    loadThoughts();
    loadTestimonials();
    loadTextReveal();
    loadFAQ();
    loadFooter();
    
    // Initialize animations
    initializeAnimations();
    
    // Initialize intersection observer for scroll animations
    initializeScrollAnimations();
    
    // Initialize smooth scrolling
    initializeSmoothScrolling();
    
    console.log('Flowkar website initialized successfully!');
}

// Load Navbar
function loadNavbar() {
    const navbarPlaceholder = document.getElementById('navbar-placeholder');
    if (navbarPlaceholder) {
        navbarPlaceholder.innerHTML = `
            <nav class="navbar navbar-expand-lg navbar-custom">
                <div class="container-fluid" style="max-width: 1250px;">
                    <!-- Logo -->
                    <a class="navbar-brand" href="#home">
                        <img src="src/assets/logo.svg" alt="Flowkar Logo" class="navbar-brand-img">
                    </a>
                    
                    <!-- Desktop Navigation -->
                    <div class="d-none d-lg-flex flex-grow-1 justify-content-center">
                        <div class="navbar-nav-center d-flex">
                            <a class="nav-link nav-link-custom active" href="#home" onclick="scrollToTop()">Home</a>
                            <a class="nav-link nav-link-custom" href="#solutions" onclick="scrollToSolutions()">Solutions</a>
                            <a class="nav-link nav-link-custom" href="blogs.html">Blogs</a>
                            <a class="nav-link nav-link-custom" href="about.html">About Us</a>
                            <a class="nav-link nav-link-custom" href="contact.html">Contact Us</a>
                        </div>
                    </div>
                    
                    <!-- Desktop Auth Buttons -->
                    <div class="d-none d-lg-flex align-items-center">
                        <button class="btn btn-signup me-3" onclick="handleSignUp()">Sign Up</button>
                        <button class="btn btn-login" onclick="handleLogin()">
                            <span>Login</span>
                            <img src="src/assets/Navbar/leftArrow.svg" alt="Arrow" width="30" height="30">
                        </button>
                    </div>
                    
                    <!-- Mobile Menu Toggle -->
                    <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#mobileMenu">
                        <img src="src/assets/hamburgerMenu.svg" alt="Menu" width="36" height="36" id="menu-icon">
                    </button>
                </div>
                
                <!-- Mobile Menu -->
                <div class="collapse navbar-collapse d-lg-none" id="mobileMenu">
                    <div class="container-fluid">
                        <div class="py-3 border-bottom mb-3">
                            <button class="btn btn-signup w-100 mb-2" onclick="handleSignUp()">Sign Up</button>
                            <button class="btn btn-login w-100" onclick="handleLogin()">
                                <span>Login</span>
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </button>
                        </div>
                        <a class="nav-link nav-link-custom d-block py-2" href="#home" onclick="scrollToTop(); closeMenu()">Home</a>
                        <a class="nav-link nav-link-custom d-block py-2" href="#solutions" onclick="scrollToSolutions(); closeMenu()">Solutions</a>
                        <a class="nav-link nav-link-custom d-block py-2" href="blogs.html" onclick="closeMenu()">Blogs</a>
                        <a class="nav-link nav-link-custom d-block py-2" href="about.html" onclick="closeMenu()">About Us</a>
                        <a class="nav-link nav-link-custom d-block py-2" href="contact.html" onclick="closeMenu()">Contact Us</a>
                    </div>
                </div>
            </nav>
        `;
    }
}

// Load Hero Section
function loadHeroSection() {
    const heroPlaceholder = document.getElementById('hero-section');
    if (heroPlaceholder) {
        heroPlaceholder.innerHTML = `
            <div class="hero-section d-flex align-items-center">
                <div class="container-fluid" style="max-width: 1250px;">
                    <div class="row align-items-center min-vh-100">
                        <!-- Left Content -->
                        <div class="col-12 col-md-6 text-center text-md-start">
                            <div class="hero-content">
                                <h1 class="hero-title">
                                    Create<br>
                                    Schedule<br>
                                    Go Viral<br>
                                    <span>All in One Place</span>
                                </h1>
                                <p class="hero-subtitle">
                                    Trusted by creators, loved by influencers, designed for growth.<br>
                                    Create smarter. Reach further. Stay ahead.
                                </p>
                            </div>
                        </div>
                        
                        <!-- Right Image Gallery -->
                        <div class="col-12 col-md-6">
                            <div class="image-gallery">
                                <!-- Mobile Gallery -->
                                <div class="image-gallery-mobile d-md-none">
                                    <div class="gallery-row">
                                        <div class="animate-scroll-left d-flex">
                                            ${generateMobileGalleryImages('row1')}
                                        </div>
                                    </div>
                                    <div class="gallery-row">
                                        <div class="animate-scroll-right d-flex">
                                            ${generateMobileGalleryImages('row2')}
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Desktop Gallery -->
                                <div class="image-gallery-desktop d-none d-md-flex">
                                    <div class="gallery-column me-3">
                                        <div class="animate-scroll-up">
                                            ${generateDesktopGalleryImages('left')}
                                        </div>
                                    </div>
                                    <div class="gallery-column ms-3">
                                        <div class="animate-scroll-down">
                                            ${generateDesktopGalleryImages('right')}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

// Generate mobile gallery images
function generateMobileGalleryImages(row) {
    const images = {
        row1: [
            { src: 'src/assets/Hero/Pinterest.svg', alt: 'Pinterest' },
            { src: 'src/assets/Hero/Dummy-01.avif', alt: 'Dummy' },
            { src: 'src/assets/Hero/Instagram.svg', alt: 'Instagram' },
            { src: 'src/assets/Hero/Dummy-02.avif', alt: 'Dummy' }
        ],
        row2: [
            { src: 'src/assets/Hero/Dummy-03.avif', alt: 'Dummy-03' },
            { src: 'src/assets/Hero/LinkedIn.svg', alt: 'LinkedIn' },
            { src: 'src/assets/Hero/Dummy-04.avif', alt: 'Dummy-04' },
            { src: 'src/assets/Hero/Dummy-05.avif', alt: 'Dummy-05' }
        ]
    };
    
    const rowImages = images[row];
    const tripleImages = [...rowImages, ...rowImages, ...rowImages];
    
    return tripleImages.map(img => 
        `<img src="${img.src}" alt="${img.alt}" class="gallery-image gallery-image-mobile">`
    ).join('');
}

// Generate desktop gallery images
function generateDesktopGalleryImages(column) {
    const images = {
        left: [
            { src: 'src/assets/Hero/Pinterest.svg', alt: 'Pinterest', height: '169px' },
            { src: 'src/assets/Hero/Dummy-01.avif', alt: 'Dummy', height: '310px' },
            { src: 'src/assets/Hero/Instagram.svg', alt: 'Instagram', height: '173px' },
            { src: 'src/assets/Hero/Dummy-02.avif', alt: 'Dummy', height: '250px' }
        ],
        right: [
            { src: 'src/assets/Hero/Dummy-03.avif', alt: 'Dummy-03', height: '250px' },
            { src: 'src/assets/Hero/LinkedIn.svg', alt: 'LinkedIn', height: '169px' },
            { src: 'src/assets/Hero/Dummy-04.avif', alt: 'Dummy-04', height: '250px' },
            { src: 'src/assets/Hero/Dummy-05.avif', alt: 'Dummy-05', height: '240px' }
        ]
    };
    
    const columnImages = images[column];
    const doubleImages = [...columnImages, ...columnImages];
    
    return doubleImages.map(img => 
        `<img src="${img.src}" alt="${img.alt}" class="gallery-image-desktop" style="height: ${img.height};">`
    ).join('');
}

// Load Management Features Section
function loadManagementFeatures() {
    const placeholder = document.getElementById('management-features');
    if (placeholder) {
        placeholder.innerHTML = `
            <div class="management-features py-5" id="solutions-section">
                <div class="container-fluid" style="max-width: 1200px;">
                    <!-- Section Header -->
                    <div class="text-center mb-5">
                        <div class="d-inline-flex align-items-center justify-content-center border border-white border-opacity-25 bg-white bg-opacity-10 px-3 py-2 rounded mb-3">
                            <img src="src/assets/Management/IconHeader.svg" alt="Solutions Icon" width="18" height="18" class="me-2">
                            <span class="text-white fw-medium" style="font-size: 14px;">Solutions</span>
                        </div>
                        <h2 class="text-white fs-2 fw-medium mb-4">
                            Social Media Management Solutions - Flowkar
                        </h2>
                    </div>

                    <!-- Features Grid -->
                    <div class="row g-4 px-3">
                        ${generateFeatureCards()}
                    </div>
                </div>
            </div>
        `;
    }
}

// Generate feature cards
function generateFeatureCards() {
    const featuresData = [
        {
            icon: 'src/assets/Management/Icon_01.svg',
            title: 'Plan, Publish and Perform Like a Pro',
            description: 'Join the platform built for creators, teams, and brands to get their work done.'
        },
        {
            icon: 'src/assets/Management/Icon_02.svg',
            title: 'Post & Schedule across all social media platforms from a single dashboard',
            description: 'Manage all your social platforms - your content, your brand.'
        },
        {
            icon: 'src/assets/Management/Icon_03.svg',
            title: 'Marketing Tools to help influencers and brands maximize reach and engagement',
            description: 'Build tools for influencers & brands to grow online reach and boost engagement.'
        },
        {
            icon: 'src/assets/Management/Icon_04.svg',
            title: 'Scheduling & Reports with Performance insights for posts, reels, and audience growth',
            description: 'Understand your audience, optimize your content, and grow your following.'
        },
        {
            icon: 'src/assets/Management/Icon_05.svg',
            title: 'Team Access & Roles for content collaboration and approval',
            description: 'Manage team roles and permissions for seamless content collaboration.'
        },
        {
            icon: 'src/assets/Management/Icon_06.svg',
            title: 'Comment & Message Management for real-time engagement',
            description: 'Keep track of comments, replies - reply back, and build relationships.'
        }
    ];

    return featuresData.map((feature, index) => `
        <div class="col-12 col-md-6 col-lg-4">
            <div class="feature-card h-100 d-flex flex-column animate-on-scroll">
                <!-- Icon -->
                <div class="mb-4">
                    <img src="${feature.icon}" alt="Feature Icon" class="feature-card-icon" width="48" height="48">
                </div>

                <!-- Title -->
                <h3 class="feature-card-title fs-5 fw-normal mb-3">
                    ${feature.title}
                </h3>

                <!-- Description -->
                <p class="feature-card-description flex-grow-1 mb-4">
                    ${feature.description}
                </p>

                <!-- Read More Button -->
                <button class="btn d-flex align-items-center justify-content-between border border-white border-opacity-40 text-white bg-transparent hover-lift"
                        style="width: 142px; height: 38px; padding: 4px 4px 4px 20px; border-radius: 8px; font-size: 16px; transition: all 0.2s ease;"
                        onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'"
                        onmouseout="this.style.backgroundColor='transparent'">
                    <span>Read More</span>
                    <img src="src/assets/Management/RightArrow.svg" alt="Arrow" width="30" height="30">
                </button>
            </div>
        </div>
    `).join('');
}

function loadSocialMediaIcons() {
    const placeholder = document.getElementById('social-media-icons');
    if (placeholder) {
        placeholder.innerHTML = `
            <section class="py-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                <div class="container-fluid" style="max-width: 1200px;">
                    <div class="text-center mb-5">
                        <h2 class="fs-2 fw-bold mb-3" style="color: var(--primary-brown);">
                            Connect All Your Social Platforms
                        </h2>
                        <p class="fs-5 text-muted">
                            Manage your presence across all major social media platforms from one dashboard
                        </p>
                    </div>

                    <div class="d-flex justify-content-center align-items-center flex-wrap gap-4 py-4">
                        ${generateSocialIcons()}
                    </div>
                </div>
            </section>
        `;
    }
}

function generateSocialIcons() {
    const socialIcons = [
        { icon: 'src/assets/svg_icon/youtube-icon.svg', alt: 'YouTube', color: '#FF0000' },
        { icon: 'src/assets/svg_icon/reddit.svg', alt: 'Reddit', color: '#FF4500' },
        { icon: 'src/assets/svg_icon/instagram.svg', alt: 'Instagram', color: '#E4405F' },
        { icon: 'src/assets/svg_icon/facebook-icon.svg', alt: 'Facebook', color: '#1877F2' },
        { icon: 'src/assets/svg_icon/twitter-icon.svg', alt: 'X (Twitter)', color: '#000000' },
        { icon: 'src/assets/svg_icon/pintrest-icon.svg', alt: 'Pinterest', color: '#BD081C' },
        { icon: 'src/assets/svg_icon/linkdin-icon.svg', alt: 'LinkedIn', color: '#0A66C2' },
        { icon: 'src/assets/svg_icon/thread.svg', alt: 'Threads', color: '#000000' }
    ];

    return socialIcons.map((social, index) => `
        <div class="social-icon bg-white rounded-circle d-flex align-items-center justify-content-center shadow-sm hover-lift animate-on-scroll"
             style="width: 80px; height: 80px; transition: all 0.3s ease; animation-delay: ${index * 0.1}s;"
             onmouseover="this.style.transform='translateY(-5px) scale(1.1)'; this.style.boxShadow='0 8px 25px rgba(0,0,0,0.15)';"
             onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)';">
            <img src="${social.icon}" alt="${social.alt}" width="40" height="40" class="img-fluid">
        </div>
    `).join('');
}

function loadInfoCard() {
    const placeholder = document.getElementById('info-card');
    if (placeholder) {
        placeholder.innerHTML = `
            <section class="py-5">
                <div class="container-fluid" style="max-width: 1100px;">
                    <div class="rounded-5 p-4 p-lg-5" style="background-color: var(--primary-brown);">
                        <div class="row g-4 g-lg-5 align-items-stretch">
                            <!-- Left Side - Main Feature Card -->
                            <div class="col-12 col-lg-6">
                                <div class="position-relative h-100" style="min-height: 400px;">
                                    <div class="rounded-4 overflow-hidden h-100 position-relative">
                                        <img src="src/assets/InfoCard/LeftImage.svg" alt="Unified Dashboard"
                                             class="w-100 h-100 object-fit-cover rounded-4">
                                        <!-- Blur overlay at bottom -->
                                        <div class="position-absolute bottom-0 start-0 end-0"
                                             style="height: 128px; background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0.4), transparent); backdrop-filter: blur(4px); border-radius: 0 0 1rem 1rem;"></div>
                                        <!-- Title positioned on blur -->
                                        <div class="position-absolute bottom-0 start-0 end-0 p-4">
                                            <h2 class="text-white fs-4 fw-semibold lh-sm mb-0">
                                                Unified Dashboard For Instagram, Facebook, LinkedIn, YouTube, And More
                                            </h2>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Side - Feature List -->
                            <div class="col-12 col-lg-6 d-flex flex-column justify-content-center">
                                <div class="d-flex flex-column gap-4 gap-lg-5">
                                    ${generateInfoCardFeatures()}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        `;
    }
}

function generateInfoCardFeatures() {
    const features = [
        {
            icon: 'src/assets/InfoCard/Icon_01.svg',
            title: 'Real-Time Comment, Like & DM Tracking',
            description: 'Track user engagement in real time to respond instantly - every second counts when capturing attention and driving meaningful interactions.'
        },
        {
            icon: 'src/assets/InfoCard/Icon_02.svg',
            title: 'Reels And Short Video Insights',
            description: 'Monitor views, reach, and engagement for every short video in real time - live insights give you clarity and control instantly.'
        },
        {
            icon: 'src/assets/InfoCard/Icon_03.svg',
            title: 'Team Collaboration & Approval Workflows',
            description: 'Seamlessly collaborate with your team - review content, share feedback, and approve projects together in one unified, efficient workflow.'
        }
    ];

    return features.map((feature, index) => `
        <div class="d-flex align-items-start gap-3 gap-sm-4 animate-on-scroll" style="animation-delay: ${index * 0.2}s;">
            <!-- Icon Container -->
            <div class="flex-shrink-0 rounded-3 d-flex align-items-center justify-content-center"
                 style="width: 70px; height: 70px;">
                <img src="${feature.icon}" alt="Feature Icon" width="70" height="70" class="img-fluid">
            </div>

            <!-- Content -->
            <div class="flex-grow-1">
                <h3 class="text-white fs-5 fw-semibold mb-2 lh-sm">
                    ${feature.title}
                </h3>
                <p class="text-white-50 mb-0 lh-base">
                    ${feature.description}
                </p>
            </div>
        </div>
    `).join('');
}

function loadAboutUsHero() {
    const placeholder = document.getElementById('about-us-hero');
    if (placeholder) {
        placeholder.innerHTML = `
            <section class="bg-white py-5">
                <div class="container-fluid" style="max-width: 1100px;">
                    <div class="row g-5 align-items-center">
                        <!-- Left side - Text content -->
                        <div class="col-12 col-lg-6">
                            <div class="pe-lg-4">
                                <h2 class="display-4 fw-semibold mb-4" style="color: var(--primary-brown);">
                                    About Us
                                </h2>
                                <div class="text-muted lh-lg">
                                    <p class="mb-4">
                                        We built Flowkar to simplify social media management for creators, brands, and teams who want to move fast, stay organized, and engage smarter. With real-time dashboards, powerful analytics, and collaborative tools, Flowkar helps you track performance, plan content, and grow your digital presence - all from one sleek platform.
                                    </p>
                                    <p class="mb-0">
                                        Social media doesn't wait. Neither should you. Flowkar is your go-to space for scheduling posts, reviewing insights, tracking engagement, and collaborating with your team - without the chaos. Whether you're a solo creator or scaling a brand, Flowkar helps you keep your flow.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Right side - Team images grid -->
                        <div class="col-12 col-lg-6 d-flex justify-content-center justify-content-lg-end">
                            <div class="row g-3" style="max-width: 400px;">
                                ${generateTeamImages()}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        `;
    }
}

function generateTeamImages() {
    const teamImages = [
        { src: 'src/assets/AboutUs/Dummy_01.svg', rounded: '26px' },
        { src: 'src/assets/AboutUs/Dummy_02.svg', rounded: '60px' },
        { src: 'src/assets/AboutUs/Dummy_03.svg', rounded: '114px' },
        { src: 'src/assets/AboutUs/Dummy_04.svg', rounded: '115px' },
        { src: 'src/assets/AboutUs/Dummy_05.svg', rounded: '26px' },
        { src: 'src/assets/AboutUs/Dummy_06.svg', rounded: '60px' }
    ];

    return teamImages.map((image, index) => `
        <div class="col-4">
            <div class="overflow-hidden shadow hover-lift animate-on-scroll"
                 style="border-radius: ${image.rounded}; width: 120px; height: 120px; animation-delay: ${index * 0.1}s;"
                 onmouseover="this.querySelector('img').style.transform='scale(1.05)'"
                 onmouseout="this.querySelector('img').style.transform='scale(1)'">
                <img src="${image.src}" alt="Team member ${index + 1}"
                     class="w-100 h-100 object-fit-cover"
                     style="transition: transform 0.3s ease;">
            </div>
        </div>
    `).join('');
}

function loadThoughts() {
    const placeholder = document.getElementById('thoughts');
    if (placeholder) {
        placeholder.innerHTML = `
            <section class="py-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);">
                <div class="container-fluid" style="max-width: 1200px;">
                    <div class="text-center mb-5">
                        <h2 class="display-5 fw-bold mb-3" style="color: var(--primary-brown);">
                            Our Thoughts & Vision
                        </h2>
                        <p class="fs-5 text-muted">
                            Building the future of social media management
                        </p>
                    </div>

                    <div class="row g-4">
                        <div class="col-12 col-md-6 col-lg-4">
                            <div class="card h-100 border-0 shadow-sm hover-lift animate-on-scroll">
                                <div class="card-body p-4">
                                    <h5 class="card-title fw-bold mb-3" style="color: var(--primary-brown);">Innovation First</h5>
                                    <p class="card-text text-muted">We believe in pushing boundaries and creating tools that don't just follow trends, but set them.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-4">
                            <div class="card h-100 border-0 shadow-sm hover-lift animate-on-scroll" style="animation-delay: 0.1s;">
                                <div class="card-body p-4">
                                    <h5 class="card-title fw-bold mb-3" style="color: var(--primary-brown);">User-Centric Design</h5>
                                    <p class="card-text text-muted">Every feature we build starts with understanding what creators and brands actually need.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-4">
                            <div class="card h-100 border-0 shadow-sm hover-lift animate-on-scroll" style="animation-delay: 0.2s;">
                                <div class="card-body p-4">
                                    <h5 class="card-title fw-bold mb-3" style="color: var(--primary-brown);">Seamless Experience</h5>
                                    <p class="card-text text-muted">Complex workflows made simple. Powerful features that feel intuitive and natural to use.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        `;
    }
}

function loadTestimonials() {
    const placeholder = document.getElementById('testimonials');
    if (placeholder) {
        placeholder.innerHTML = `
            <section class="py-5">
                <div class="container-fluid" style="max-width: 1200px;">
                    <div class="text-center mb-5">
                        <h2 class="display-5 fw-bold mb-3" style="color: var(--primary-brown);">
                            What Our Users Say
                        </h2>
                        <p class="fs-5 text-muted">
                            Trusted by creators and brands worldwide
                        </p>
                    </div>

                    <div class="row g-4">
                        ${generateTestimonials()}
                    </div>
                </div>
            </section>
        `;
    }
}

function generateTestimonials() {
    const testimonials = [
        {
            name: "Sarah Johnson",
            role: "Content Creator",
            text: "Flowkar has completely transformed how I manage my social media. The unified dashboard saves me hours every week!",
            avatar: "src/assets/Hero/Dummy-01.avif"
        },
        {
            name: "Mike Chen",
            role: "Marketing Manager",
            text: "The analytics and team collaboration features are game-changers. Our engagement has increased by 300% since using Flowkar.",
            avatar: "src/assets/Hero/Dummy-02.avif"
        },
        {
            name: "Emma Davis",
            role: "Brand Strategist",
            text: "Finally, a platform that understands what creators need. The scheduling and insights are incredibly powerful yet easy to use.",
            avatar: "src/assets/Hero/Dummy-03.avif"
        }
    ];

    return testimonials.map((testimonial, index) => `
        <div class="col-12 col-md-6 col-lg-4">
            <div class="testimonial-card animate-on-scroll" style="animation-delay: ${index * 0.1}s;">
                <div class="d-flex align-items-center mb-3">
                    <img src="${testimonial.avatar}" alt="${testimonial.name}"
                         class="testimonial-avatar me-3" width="60" height="60">
                    <div>
                        <div class="testimonial-name">${testimonial.name}</div>
                        <div class="testimonial-role">${testimonial.role}</div>
                    </div>
                </div>
                <p class="testimonial-text">"${testimonial.text}"</p>
            </div>
        </div>
    `).join('');
}

function loadTextReveal() {
    const placeholder = document.getElementById('text-reveal');
    if (placeholder) {
        placeholder.innerHTML = `
            <section class="py-5" style="background: var(--primary-brown);">
                <div class="container-fluid" style="max-width: 1000px;">
                    <div class="text-center text-white">
                        <h2 class="display-4 fw-bold mb-4 animate-on-scroll">
                            Ready to Transform Your Social Media Strategy?
                        </h2>
                        <p class="fs-4 mb-5 opacity-75 animate-on-scroll" style="animation-delay: 0.2s;">
                            Join thousands of creators and brands who trust Flowkar to grow their digital presence
                        </p>
                        <div class="animate-on-scroll" style="animation-delay: 0.4s;">
                            <button class="btn btn-light btn-lg px-5 py-3 fw-bold" onclick="handleSignUp()">
                                Get Started Today
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        `;
    }
}

function loadFAQ() {
    const placeholder = document.getElementById('faq');
    if (placeholder) {
        placeholder.innerHTML = `
            <section class="py-5">
                <div class="container-fluid" style="max-width: 800px;">
                    <div class="text-center mb-5">
                        <h2 class="display-5 fw-bold mb-3" style="color: var(--primary-brown);">
                            Frequently Asked Questions
                        </h2>
                        <p class="fs-5 text-muted">
                            Everything you need to know about Flowkar
                        </p>
                    </div>

                    <div class="accordion" id="faqAccordion">
                        ${generateFAQItems()}
                    </div>
                </div>
            </section>
        `;
    }
}

function generateFAQItems() {
    const faqs = [
        {
            question: "What social media platforms does Flowkar support?",
            answer: "Flowkar supports all major social media platforms including Instagram, Facebook, LinkedIn, YouTube, Twitter/X, Pinterest, Reddit, and Threads."
        },
        {
            question: "Can I collaborate with my team on Flowkar?",
            answer: "Yes! Flowkar offers comprehensive team collaboration features including role-based access, approval workflows, and real-time collaboration tools."
        },
        {
            question: "Does Flowkar provide analytics and insights?",
            answer: "Absolutely. Flowkar provides detailed analytics for all your posts, including engagement metrics, reach, impressions, and audience insights to help you optimize your content strategy."
        },
        {
            question: "Is there a free trial available?",
            answer: "Yes, we offer a free trial so you can explore all of Flowkar's features before committing to a subscription plan."
        },
        {
            question: "How does the scheduling feature work?",
            answer: "Our scheduling feature allows you to plan and schedule posts across all your social media platforms from a single dashboard. You can schedule individual posts or bulk upload content with our advanced scheduling tools."
        }
    ];

    return faqs.map((faq, index) => `
        <div class="accordion-item border-0 mb-3 animate-on-scroll" style="animation-delay: ${index * 0.1}s;">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed fw-semibold" type="button"
                        data-bs-toggle="collapse" data-bs-target="#faq${index}"
                        style="background: #f8f9fa; color: var(--primary-brown); border-radius: 0.5rem;">
                    ${faq.question}
                </button>
            </h2>
            <div id="faq${index}" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                <div class="accordion-body text-muted lh-lg">
                    ${faq.answer}
                </div>
            </div>
        </div>
    `).join('');
}

function loadFooter() {
    const placeholder = document.getElementById('footer-placeholder');
    if (placeholder) {
        placeholder.innerHTML = `
            <footer class="footer-custom mt-5">
                <!-- Main Footer Content -->
                <div class="row g-4 text-center text-md-start">
                    <!-- Flowkar Section -->
                    <div class="col-12 col-md-4">
                        <div class="d-flex flex-column align-items-center align-items-md-start">
                            <div class="mb-4">
                                <img src="src/assets/logo.svg" alt="Flowkar" width="128" height="auto">
                            </div>
                            <p class="text-muted" style="font-size: 16px; line-height: 1.75; max-width: 320px;">
                                Flowkar lets you create, schedule, and share content across platforms - helping you grow your audience with ease and efficiency.
                            </p>
                        </div>
                    </div>

                    <!-- Contact Us Section -->
                    <div class="col-12 col-md-4">
                        <div class="d-flex flex-column align-items-center align-items-md-start">
                            <h3 class="fs-5 fw-semibold text-dark mb-4">Contact Us</h3>
                            <div class="mb-3">
                                <p class="text-dark mb-2" style="font-size: 16px;">We are always happy to assist you</p>
                                <a href="mailto:<EMAIL>" class="footer-link">
                                    Email: <span class="text-decoration-underline"><EMAIL></span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Check it out Section -->
                    <div class="col-12 col-md-4">
                        <div class="d-flex flex-column align-items-center align-items-md-start">
                            <h3 class="fs-5 fw-semibold text-dark mb-4">Check it out</h3>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <a href="index.html" class="footer-link">Home</a>
                                </li>
                                <li class="mb-2">
                                    <a href="solutions.html" class="footer-link">Solutions</a>
                                </li>
                                <li class="mb-2">
                                    <a href="about.html" class="footer-link">About Us</a>
                                </li>
                                <li class="mb-2">
                                    <a href="blogs.html" class="footer-link">Blog</a>
                                </li>
                                <li class="mb-2">
                                    <a href="contact.html" class="footer-link">Contact Us</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Bottom Dark Section -->
                <div class="bg-dark text-white py-3 mt-4 rounded-bottom" style="background-color: var(--primary-brown) !important; border-radius: 0 0 30px 30px;">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center text-center text-sm-start">
                        <p class="mb-2 mb-sm-0" style="font-size: 14px;">
                            © 2025 Flowkar, Inc. - All Rights Reserved
                        </p>
                        <div class="d-flex gap-4">
                            <a href="terms.html" class="text-white text-decoration-none" style="font-size: 14px;">
                                Terms of Use
                            </a>
                            <a href="privacy.html" class="text-white text-decoration-none" style="font-size: 14px;">
                                Privacy Policy
                            </a>
                        </div>
                    </div>
                </div>
            </footer>
        `;
    }
}

// Initialize animations
function initializeAnimations() {
    // Add any animation initialization code here
    console.log('Animations initialized');
}

// Initialize scroll animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('in-view');
            }
        });
    }, observerOptions);
    
    // Observe elements with animation classes
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

// Initialize smooth scrolling
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}
