import { useEffect } from 'react';

const PerformanceMonitor = () => {
  useEffect(() => {
    // Monitor Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          console.log('Navigation Timing:', {
            'DNS Lookup': entry.domainLookupEnd - entry.domainLookupStart,
            'TCP Connection': entry.connectEnd - entry.connectStart,
            'Request': entry.responseStart - entry.requestStart,
            'Response': entry.responseEnd - entry.responseStart,
            'DOM Processing': entry.domContentLoadedEventStart - entry.responseEnd,
            'Total Load Time': entry.loadEventEnd - entry.navigationStart
          });
        }
        
        if (entry.entryType === 'largest-contentful-paint') {
          console.log('LCP (Largest Contentful Paint):', entry.startTime + 'ms');
        }
        
        if (entry.entryType === 'first-input') {
          console.log('FID (First Input Delay):', entry.processingStart - entry.startTime + 'ms');
        }
        
        if (entry.entryType === 'layout-shift') {
          console.log('CLS (Cumulative Layout Shift):', entry.value);
        }
      }
    });

    // Observe different performance metrics
    try {
      observer.observe({ entryTypes: ['navigation', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
    } catch (e) {
      console.log('Performance Observer not supported');
    }

    // Monitor resource loading
    const resourceObserver = new PerformanceObserver((list) => {
      const resources = list.getEntries();
      const slowResources = resources.filter(resource => resource.duration > 1000);
      
      if (slowResources.length > 0) {
        console.log('Slow loading resources (>1s):', slowResources.map(r => ({
          name: r.name,
          duration: r.duration + 'ms',
          size: r.transferSize + ' bytes'
        })));
      }
    });

    try {
      resourceObserver.observe({ entryTypes: ['resource'] });
    } catch (e) {
      console.log('Resource Observer not supported');
    }

    // Monitor memory usage (if available)
    if ('memory' in performance) {
      const logMemory = () => {
        console.log('Memory Usage:', {
          'Used JS Heap Size': (performance.memory.usedJSHeapSize / 1048576).toFixed(2) + ' MB',
          'Total JS Heap Size': (performance.memory.totalJSHeapSize / 1048576).toFixed(2) + ' MB',
          'JS Heap Size Limit': (performance.memory.jsHeapSizeLimit / 1048576).toFixed(2) + ' MB'
        });
      };
      
      // Log memory usage every 10 seconds
      const memoryInterval = setInterval(logMemory, 10000);
      
      return () => {
        clearInterval(memoryInterval);
        observer.disconnect();
        resourceObserver.disconnect();
      };
    }

    return () => {
      observer.disconnect();
      resourceObserver.disconnect();
    };
  }, []);

  return null; // This component doesn't render anything
};

export default PerformanceMonitor;
