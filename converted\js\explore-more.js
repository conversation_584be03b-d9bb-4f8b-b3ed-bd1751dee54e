// explore-more.js - Handles the blog grid and pagination functionality

document.addEventListener('DOMContentLoaded', function() {
  // Initialize variables
  let blogPosts = [];
  let currentPage = 1;
  const blogsPerPage = 8;
  let totalPages = 0;
  
  // Get DOM elements
  const blogGrid = document.getElementById('blog-grid');
  const paginationNumbers = document.getElementById('pagination-numbers');
  const prevPageButton = document.getElementById('prev-page');
  const nextPageButton = document.getElementById('next-page');
  const exploreSection = document.getElementById('explore-section');
  
  // Load blogs
  loadBlogs();
  
  // Add event listeners for pagination buttons
  prevPageButton.addEventListener('click', function() {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  });
  
  nextPageButton.addEventListener('click', function() {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  });
  
  /**
   * Load blogs from localStorage or API
   */
  function loadBlogs() {
    // Show loading state
    showLoadingState();
    
    // Clear localStorage to ensure fresh data (simulating the hard refresh behavior)
    localStorage.removeItem('flowkar_explore_blogs');
    
    // Try to get blogs from localStorage first
    const cachedBlogs = localStorage.getItem('flowkar_explore_blogs');
    
    if (cachedBlogs) {
      try {
        blogPosts = JSON.parse(cachedBlogs);
        renderBlogs();
        return;
      } catch (err) {
        console.error('Error parsing cached blogs:', err);
      }
    }
    
    // If not in cache, fetch from API
    fetch('https://flowkar.com/api/get-all-blogs/')
      .then(response => {
        if (!response.ok) throw new Error('Network response was not ok');
        return response.json();
      })
      .then(data => {
        if (data && data.data && Array.isArray(data.data)) {
          // Map API data to blog format
          const mappedBlogs = data.data.map(item => ({
            id: item.id,
            image: getImageUrl(item),
            title: item.title || 'Untitled Blog',
            description: item.keywords || '',
            category: item.category || 'General',
            readTime: item.read_time || '',
            author: item.keywords || 'Unknown Author',
            excerpt: item.keywords || '',
            content: item.keywords || '',
            tags: item.keywords ? item.keywords.split(',') : [],
          }));
          
          blogPosts = mappedBlogs;
          
          // Store in localStorage
          localStorage.setItem('flowkar_explore_blogs', JSON.stringify(mappedBlogs));
          
          renderBlogs();
        } else {
          showError('No blogs found.');
        }
      })
      .catch(error => {
        console.error('Error fetching blogs:', error);
        showError('Failed to load blogs. Please try again later.');
      });
  }
  
  /**
   * Render blogs for the current page
   */
  function renderBlogs() {
    // Calculate pagination
    totalPages = Math.ceil(blogPosts.length / blogsPerPage);
    const indexOfLastBlog = currentPage * blogsPerPage;
    const indexOfFirstBlog = indexOfLastBlog - blogsPerPage;
    const currentBlogs = blogPosts.slice(indexOfFirstBlog, indexOfLastBlog);
    
    // Clear the blog grid
    blogGrid.innerHTML = '';
    
    // If no blogs, show message
    if (blogPosts.length === 0) {
      blogGrid.innerHTML = '<div class="col-12 text-center"><p>No blogs found.</p></div>';
      paginationNumbers.innerHTML = '';
      prevPageButton.disabled = true;
      nextPageButton.disabled = true;
      return;
    }
    
    // Render blogs
    currentBlogs.forEach(post => {
      const blogCard = document.createElement('div');
      blogCard.className = 'col';
      blogCard.innerHTML = `
        <div class="card bg-transparent border-0 h-100 blog-card" data-slug="${createSlug(post.title)}">
          <img src="${post.image}" alt="${post.title}" class="card-img-top rounded-4 img-fluid" style="height: 300px; object-fit: cover;">
          <div class="card-body px-4">
            <h3 class="fw-light fs-6 mb-1 text-white line-clamp-2">${post.title}</h3>
            <p class="text-white-50 small line-clamp-2">${post.description}</p>
            <div class="text-white-50 small mt-2">
              <span class="me-2">${post.category}</span> | <span class="ms-2">${post.readTime}</span>
            </div>
          </div>
        </div>
      `;
      
      blogGrid.appendChild(blogCard);
    });
    
    // Add click event listeners to blog cards
    document.querySelectorAll('.blog-card').forEach(card => {
      card.addEventListener('click', function() {
        const slug = this.dataset.slug;
        window.location.href = `single-blog.html?slug=${slug}`;
      });
    });
    
    // Render pagination
    renderPagination();
    
    // Update button states
    prevPageButton.disabled = currentPage === 1;
    nextPageButton.disabled = currentPage === totalPages;
  }
  
  /**
   * Render pagination numbers
   */
  function renderPagination() {
    paginationNumbers.innerHTML = '';
    
    const pageNumbers = getPageNumbers();
    
    pageNumbers.forEach(num => {
      if (num === '...') {
        const ellipsis = document.createElement('span');
        ellipsis.className = 'w-32px h-32px d-flex align-items-center justify-content-center text-white-50';
        ellipsis.textContent = '...';
        paginationNumbers.appendChild(ellipsis);
      } else {
        const button = document.createElement('button');
        button.className = `w-32px h-32px rounded-2 d-flex align-items-center justify-content-center ${num === currentPage ? 'bg-white text-dark' : 'bg-white-10 text-white-50 hover-bg-white-20'}`;
        button.textContent = num;
        button.addEventListener('click', () => handlePageChange(num));
        paginationNumbers.appendChild(button);
      }
    });
  }
  
  /**
   * Get page numbers to display in pagination
   * @returns {Array} - Array of page numbers and ellipses
   */
  function getPageNumbers() {
    const pageNumbers = [];
    
    // Show up to 3 pages before and after current page, always show first and last
    for (let i = 1; i <= totalPages; i++) {
      if (
        i === 1 ||
        i === totalPages ||
        (i >= currentPage - 2 && i <= currentPage + 2)
      ) {
        pageNumbers.push(i);
      } else if (
        (i === currentPage - 3 && currentPage - 3 > 1) ||
        (i === currentPage + 3 && currentPage + 3 < totalPages)
      ) {
        pageNumbers.push('...');
      }
    }
    
    // Remove consecutive duplicates of '...'
    return pageNumbers.filter(
      (num, idx, arr) => num !== '...' || arr[idx - 1] !== '...'
    );
  }
  
  /**
   * Handle page change
   * @param {number} page - The page number to change to
   */
  function handlePageChange(page) {
    if (page < 1 || page > totalPages) return;
    
    // Show loading state
    showLoadingState();
    
    // Update current page
    currentPage = page;
    
    // Simulate loading delay for UX
    setTimeout(() => {
      renderBlogs();
      
      // Smooth scroll to Explore More section
      exploreSection.scrollIntoView({ behavior: 'smooth' });
    }, 400);
  }
  
  /**
   * Show loading state
   */
  function showLoadingState() {
    blogGrid.innerHTML = `
      <div class="col">
        <div class="placeholder-glow">
          <div class="placeholder col-12 rounded-4" style="height: 300px;"></div>
          <h5 class="placeholder col-8 mt-3"></h5>
          <p class="placeholder col-12"></p>
          <div class="placeholder col-6"></div>
        </div>
      </div>
      <div class="col">
        <div class="placeholder-glow">
          <div class="placeholder col-12 rounded-4" style="height: 300px;"></div>
          <h5 class="placeholder col-8 mt-3"></h5>
          <p class="placeholder col-12"></p>
          <div class="placeholder col-6"></div>
        </div>
      </div>
      <div class="col">
        <div class="placeholder-glow">
          <div class="placeholder col-12 rounded-4" style="height: 300px;"></div>
          <h5 class="placeholder col-8 mt-3"></h5>
          <p class="placeholder col-12"></p>
          <div class="placeholder col-6"></div>
        </div>
      </div>
      <div class="col">
        <div class="placeholder-glow">
          <div class="placeholder col-12 rounded-4" style="height: 300px;"></div>
          <h5 class="placeholder col-8 mt-3"></h5>
          <p class="placeholder col-12"></p>
          <div class="placeholder col-6"></div>
        </div>
      </div>
    `;
  }
  
  /**
   * Show error message
   * @param {string} message - The error message to display
   */
  function showError(message) {
    blogGrid.innerHTML = `<div class="col-12 text-center"><div class="alert alert-danger">${message}</div></div>`;
    paginationNumbers.innerHTML = '';
    prevPageButton.disabled = true;
    nextPageButton.disabled = true;
  }
  
  /**
   * Get image URL from blog data
   * @param {Object} item - The blog data
   * @returns {string} - The image URL
   */
  function getImageUrl(item) {
    if (item.video_thumbnail) {
      return item.video_thumbnail.startsWith('http')
        ? item.video_thumbnail
        : `https://flowkar.com${item.video_thumbnail.startsWith('/') ? item.video_thumbnail : '/' + item.video_thumbnail}`;
    }
    
    if (item.banner) {
      return item.banner.startsWith('http')
        ? item.banner
        : `https://flowkar.com${item.banner.startsWith('/') ? item.banner : '/' + item.banner}`;
    }
    
    // Default image
    return '../src/assets/Blog/Image_10.svg';
  }
  
  /**
   * Create a URL-friendly slug from a string
   * @param {string} text - The text to convert to a slug
   * @returns {string} - The slug
   */
  function createSlug(text) {
    if (!text) return '';
    
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove non-word chars
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
  }
});

// Add custom CSS
document.addEventListener('DOMContentLoaded', function() {
  const style = document.createElement('style');
  style.textContent = `
    .w-32px { width: 32px; }
    .h-32px { height: 32px; }
    .bg-white-10 { background-color: rgba(255, 255, 255, 0.1); }
    .hover-bg-white-20:hover { background-color: rgba(255, 255, 255, 0.2); }
    .hover-white:hover { color: white !important; }
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .w-90 {
      width: 90%;
    }
    .blog-card {
      cursor: pointer;
      transition: transform 0.3s;
    }
    .blog-card:hover {
      transform: translateY(-5px);
    }
  `;
  document.head.appendChild(style);
});