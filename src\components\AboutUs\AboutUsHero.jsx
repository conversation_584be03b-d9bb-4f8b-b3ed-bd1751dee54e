import React from "react";
import Dummy_01 from "../../assets/AboutUs/Dummy_01.svg";
import Dummy_02 from "../../assets/AboutUs/Dummy_02.svg";
import Dummy_03 from "../../assets/AboutUs/Dummy_03.svg";
import Dummy_04 from "../../assets/AboutUs/Dummy_04.svg";
import Dummy_05 from "../../assets/AboutUs/Dummy_05.svg";
import Dummy_06 from "../../assets/AboutUs/Dummy_06.svg";

function About_Us() {
  // Team images with different dimensions for each
  const teamImages = [
    {
      src: Dummy_01,
      width: "w-[120px]",
      height: "h-[120px]",
      rounded: "rounded-[26px]",
      smWidth: "sm:w-[140px]",
      smHeight: "sm:h-[140px]",
      lgWidth: "lg:w-[190px]",
      lgHeight: "lg:h-[190px]",
    },
    {
      src: Dummy_02,
      width: "w-[120px]",
      height: "h-[120px]",
      rounded: "rounded-[60px]",
      smWidth: "sm:w-[130px]",
      smHeight: "sm:h-[150px]",
      lgWidth: "lg:w-[190px]",
      lgHeight: "lg:h-[190px]",
    },
    {
      src: Dummy_03,
      width: "w-[120px]",
      height: "h-[120px]",
      rounded: "rounded-[114px]",
      smWidth: "sm:w-[150px]",
      smHeight: "sm:h-[130px]",
      lgWidth: "lg:w-[190px]",
      lgHeight: "lg:h-[190px]",
    },
    {
      src: Dummy_04,
      width: "w-[120px]",
      height: "h-[120px]",
      rounded: "rounded-[115px]",
      smWidth: "sm:w-[135px]",
      smHeight: "sm:h-[145px]",
      lgWidth: "lg:w-[190px]",
      lgHeight: "lg:h-[190px]",
    },
    {
      src: Dummy_05,
      width: "w-[120px]",
      height: "h-[120px]",
      rounded: "rounded-[26px]",
      smWidth: "sm:w-[145px]",
      smHeight: "sm:h-[135px]",
      lgWidth: "lg:w-[190px]",
      lgHeight: "lg:h-[190px]",
    },
    {
      src: Dummy_06,
      width: "w-[120px]",
      height: "h-[120px]",
      rounded: "rounded-[60px]",
      smWidth: "sm:w-[130px]",
      smHeight: "sm:h-[130px]",
      lgWidth: "lg:w-[190px]",
      lgHeight: "lg:h-[190px]",
    },
  ];

  return (
    <div className="bg-white md:py-16 mb-[50px] md:mb-[64px] px-4 sm:px-6 lg:px-8 ">
      <div className=" mx-auto w-[90%]">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Text content */}
          <div className="space-y-6">
            <h2 className="text-4xl font-semibold text-[#563D39] mb-6">
              About Us
            </h2>
            <div className="space-y-4 text-[#000000B2] font-normal leading-[28px]">
              <p>
                We built Flowkar to simplify social media management for
                creators, brands, and teams who want to move fast, stay
                organized, and engage smarter. With real-time dashboards,
                powerful analytics, and collaborative tools, Flowkar helps you
                track performance, plan content, and grow your digital presence
                - all from one sleek platform.
              </p>
              <p>
                Social media doesn't wait. Neither should you. Flowkar is your
                go-to space for scheduling posts, reviewing insights, tracking
                engagement, and collaborating with your team - without the
                chaos. Whether you're a solo creator or scaling a brand, Flowkar
                helps you keep your flow.
              </p>
            </div>
          </div>

          {/* Right side - Team images grid */}
          <div className="flex justify-center lg:justify-end">
            <div className="grid grid-cols-3 gap-4 ">
              {teamImages.map((imageObj, index) => (
                <div
                  key={index}
                  className={`${imageObj.width} ${imageObj.height} ${imageObj.smWidth} ${imageObj.smHeight} ${imageObj.lgWidth} ${imageObj.lgHeight} ${imageObj.rounded} overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300`}
                >
                  <img
                    src={imageObj.src}
                    alt={`Team member ${index + 1}`}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default About_Us;
