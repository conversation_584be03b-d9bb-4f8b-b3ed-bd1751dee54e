import React from "react";
import RightArrow from "../../assets/AboutUs/RightArrow.svg";
import GetStarted_01 from "../../assets/AboutUs/GetStarted_01.svg";
import GetStarted_02 from "../../assets/AboutUs/GetStarted_02.svg";
import GetStarted_03 from "../../assets/AboutUs/GetStarted_03.svg";
import GetStarted_04 from "../../assets/AboutUs/GetStarted_04.svg";
import GetStarted_05 from "../../assets/AboutUs/GetStarted_05.svg";
import GetStarted_06 from "../../assets/AboutUs/GetStarted_06.svg";
import Bg from "../../assets/Blog/Blog_bg.svg";

function GetStarted() {
  const handleRedirectPlayStore = () => {
    window.location.href =
      "https://play.google.com/store/apps/details?id=com.app.flowkar&hl=en";
  };

  const handleRedirectAppStore = () => {
    window.location.href = "https://apps.apple.com/in/app/flowkar/id6740058663";
  };
  return (
    <div
      className="px-4 sm:px-6 lg:px-0 my-[60px] w-[90%] mx-auto rounded-[40px]"
      style={{
        backgroundImage: `url(${Bg})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      {/* Text Section */}
      <div className="relative w-full  mx-auto min-h-[400px] sm:min-h-[500px] lg:min-h-[600px] rounded-[20px] sm:rounded-[30px] lg:rounded-[40px] flex flex-col gap-[20px] sm:gap-[30px] lg:gap-[40px] text-center py-[100px] sm:py-[150px] lg:py-[220px] justify-center items-center ">
        <p className="text-[#FFFFFF80] font-normal text-[16px] sm:text-[20px] md:text-[24px] lg:text-[30px] px-4 sm:px-8 lg:px-0 max-w-[800px]">
          1000+ people like you use Flowkar to build their brand on social media
          every month
        </p>
        <div className="flex flex-col xs:flex-row gap-3 sm:gap-4 mt-8 sm:mt-12 items-center md:items-start justify-center md:justify-start">
          {/* App Store Button */}
          <div
            className="bg-[#FFFFFF] backdrop-blur-sm rounded-[6px] w-full xs:w-[150px] sm:w-[163px] h-[42px] px-3 sm:px-5 py-1.5 flex items-center justify-between hover:bg-white transition-all duration-300 cursor-pointer shadow-lg"
            onClick={handleRedirectAppStore}
          >
            <div className="text-left flex-1">
              <div className="text-black text-[14px] sm:text-[16px] font-normal">
                App Store
              </div>
            </div>
            <div className="w-[26px] h-[26px] sm:w-[30px] sm:h-[30px] bg-[#563D3933] rounded-[4px] flex items-center justify-center ml-2 sm:ml-4 flex-shrink-0">
              {/* Placeholder Apple icon */}
              <svg
                className="w-4 h-4 sm:w-6 sm:h-6 text-black"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
              </svg>
            </div>
          </div>

          {/* Google Play Button */}
          <div
            className="bg-[#FFFFFF] backdrop-blur-sm rounded-[6px] w-full xs:w-[150px] sm:w-[163px] h-[42px] px-3 sm:px-5 py-1.5 flex items-center justify-between hover:bg-white transition-all duration-300 cursor-pointer shadow-lg"
            onClick={handleRedirectPlayStore}
          >
            <div className="text-left flex-1">
              <div className="text-black text-[14px] sm:text-[16px] font-normal whitespace-nowrap">
                Google Play
              </div>
            </div>
            <div className="w-[26px] h-[26px] sm:w-[30px] sm:h-[30px] bg-[#563D3933] rounded-[4px] flex items-center justify-center ml-2 sm:ml-4 flex-shrink-0">
              {/* Placeholder Google Play icon */}
              <svg
                className="w-4 h-4 sm:w-6 sm:h-6 text-black"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Floating Images */}

        {/* Mobile Layout (sm and below) */}
        <div className="sm:hidden">
          {/* Image - 01 - Top Left */}
          <div className="absolute top-[20px] left-[20px]">
            <img
              src={GetStarted_01}
              alt="Image_01"
              className="h-[80px] w-[80px] rounded-[12px]"
            />
          </div>
          {/* Image - 02 - Top Right */}
          <div className="absolute top-[10px] right-[20px]">
            <img
              src={GetStarted_02}
              alt="Image_02"
              className="h-[80px] w-[80px] rounded-[12px]"
            />
          </div>
          {/* Image - 03 - Middle Right */}
          <div className="absolute top-[170px] -right-[40px]">
            <img
              src={GetStarted_03}
              alt="Image_03"
              className="h-[100px] w-[100px] rounded-[12px]"
            />
          </div>
          {/* Image - 04 - Bottom Right */}
          <div className="absolute bottom-[10px] right-[30px]">
            <img
              src={GetStarted_04}
              alt="Image_04"
              className="h-[80px] w-[70px] rounded-[12px]"
            />
          </div>
          {/* Image - 05 - Bottom Center */}
          <div className="absolute bottom-[20px] left-[50%] transform -translate-x-1/2">
            <img
              src={GetStarted_05}
              alt="Image_05"
              className="h-[70px] w-[70px] rounded-[12px]"
            />
          </div>
          {/* Image - 06 - Bottom Left */}
          <div className="absolute bottom-[40px] left-[10px]">
            <img
              src={GetStarted_06}
              alt="Image_06"
              className="h-[90px] w-[90px] rounded-[12px]"
            />
          </div>
        </div>

        {/* Tablet Layout (md) */}
        <div className="hidden sm:block lg:hidden">
          {/* Image - 01 */}
          <div className="absolute top-[30px] left-[80px]">
            <img
              src={GetStarted_01}
              alt="Image_01"
              className="h-[120px] w-[120px] rounded-[15px]"
            />
          </div>
          {/* Image - 02 */}
          <div className="absolute top-[15px] right-[120px]">
            <img
              src={GetStarted_02}
              alt="Image_02"
              className="h-[120px] w-[120px] rounded-[15px]"
            />
          </div>
          {/* Image - 03 */}
          <div className="absolute top-[180px] right-[30px]">
            <img
              src={GetStarted_03}
              alt="Image_03"
              className="h-[120px] w-[100px] rounded-[15px]"
            />
          </div>
          {/* Image - 04 */}
          <div className="absolute bottom-[20px] right-[30px]">
            <img
              src={GetStarted_04}
              alt="Image_04"
              className="h-[120px] w-[100px] rounded-[15px]"
            />
          </div>
          {/* Image - 05 */}
          <div className="absolute bottom-[30px] left-[200px]">
            <img
              src={GetStarted_05}
              alt="Image_05"
              className="h-[100px] w-[100px] rounded-[15px]"
            />
          </div>
          {/* Image - 06 */}
          <div className="absolute bottom-[40px] -left-[40px]">
            <img
              src={GetStarted_06}
              alt="Image_06"
              className="h-[140px] w-[140px] rounded-[15px]"
            />
          </div>
        </div>

        {/* Desktop Layout (lg and above) - Original positioning */}
        <div className="hidden lg:block">
          {/* Image - 01 */}
          <div className="absolute top-[17px] left-[153px]">
            <img
              src={GetStarted_01}
              alt="Image_01"
              className="h-[180px] w-[180px] rounded-[18px]"
            />
          </div>
          {/* Image - 02 */}
          <div className="absolute -top-[28px] right-[274px]">
            <img
              src={GetStarted_02}
              alt="Image_02"
              className="h-[180px] w-[180px] rounded-[18px]"
            />
          </div>
          {/* Image - 03 */}
          <div className="absolute top-[249px] right-[36px]">
            <img
              src={GetStarted_03}
              alt="Image_03"
              className="h-[180px] w-[180px] rounded-[18px]"
            />
          </div>
          {/* Image - 04 */}
          <div className="absolute -bottom-[30px] right-[140px]">
            <img
              src={GetStarted_04}
              alt="Image_04"
              className="h-[180px] w-[160px] rounded-[18px]"
            />
          </div>
          {/* Image - 05 */}
          <div className="absolute bottom-[12px] left-[374px]">
            <img
              src={GetStarted_05}
              alt="Image_05"
              className="h-[159px] w-[159px] rounded-[18px]"
            />
          </div>
          {/* Image - 06 */}
          <div className="absolute bottom-[108px] left-[50px]">
            <img
              src={GetStarted_06}
              alt="Image_06"
              className="h-[200px] w-[200px] rounded-[18px]"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default GetStarted;
