import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import Hamburger from "../../assets/hamburgerMenu.svg";
import Close from "../../assets/close.svg";
import logo from "../../assets/logo.svg";
import leftArrow from "../../assets/Navbar/leftArrow.svg";

const Navbar = () => {
  const [toggle, setToggle] = useState(false);
  const location = useLocation();
  const currentPath = location.pathname;

  // Scroll to top on route change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location]);

  // Scroll to top function for all navigation items
  const scrollToTop = () => {
    window.scrollTo(0, 0);
  };

  const scrollToSolutions = () => {
    const el = document.getElementById("solutions-section");
    if (el) {
      const yOffset = -100; // Offset for 100px gap
      const y = el.getBoundingClientRect().top + window.pageYOffset + yOffset;
      window.scrollTo({ top: y, behavior: "smooth" });
    }
  };

  const handleRedirect = () => {
    const userdataString = localStorage.getItem("UserId");
    if (userdataString) {
      try {
        const userdata = JSON.parse(userdataString);
        // Check if userdata exists and has required properties
        if (userdata) {
          window.location.href = "https://app.flowkar.com/dashboard";
        } else {
          window.location.href = "https://app.flowkar.com/sign-in";
        }
      } catch (error) {
        console.error("Error parsing user data:", error);
        window.location.href = "https://app.flowkar.com/sign-in"; 
      }
    } else {
      window.location.href = "https://app.flowkar.com/sign-in";
    }
  };

  // Handle sign up redirect
  const handleSignUp = () => {
    window.location.href = "https://app.flowkar.com/sign-up";
  };

  return (
    <nav className="w-full bg-white dark:bg-dark[#939393] fixed top-0 z-50 shadow-md">
      <div className="md:max-w-[1250px] mx-auto px-4 flex items-center h-[80px]">
        {/* Logo - Left */}
        <div className="flex-shrink-0">
          <Link
            to="/"
            className="text-2xl font-bold text-[#939393] dark:text-white"
          >
            <img src={logo} alt="Logo" className="w-32 h-auto" />
          </Link>
        </div>

        {/* Desktop Navigation - Center */}
        <div className="hidden lg:flex flex-1 justify-center">
          <div className="flex space-x-8 items-center bg-gradient-to-r from-[#0000000D] via-[#0000001A] to-[#0000000D] rounded-[60px] pt-[14px] pr-[20px] pb-[14px] pl-[20px]">
            <Link
              to="/"
              className={`hover:text-[#563D39] font-medium transition text-[#939393] dark:text-white${currentPath === "/" ? " !text-[#563D39] font-semibold" : ""}`}
              onClick={scrollToTop}
            >
              Home
            </Link>

            <Link
              as="button"
              to="#"
              className={`hover:text-[#563D39] font-medium transition text-[#939393] dark:text-white${currentPath === "/solutions" ? " !text-[#563D39] font-semibold" : ""}`}
              onClick={e => {
                e.preventDefault();
                scrollToSolutions();
              }}
            >
              Solutions
            </Link>

            <Link
              to="/blogs"
              className={`hover:text-[#563D39] font-medium transition text-[#939393] dark:text-white${currentPath === "/blogs" ? " !text-[#563D39] font-semibold" : ""}`}
              onClick={scrollToTop}
            >
              Blogs
            </Link>

            <Link
              to="/aboutus"
              className={`hover:text-[#563D39] font-medium transition text-[#939393] dark:text-white${currentPath === "/aboutus" ? " !text-[#563D39] font-semibold" : ""}`}
              onClick={scrollToTop}
            >
              About Us
            </Link>

            <Link
              to="/contact-us"
              className={`hover:text-[#563D39] font-medium transition text-[#939393] dark:text-white${currentPath === "/contact-us" ? " !text-[#563D39] font-semibold" : ""}`}
              onClick={scrollToTop}
            >
              Contact Us
            </Link>
          </div>
        </div>

        {/* Sign Up and Login Buttons - Right */}
        <div className="hidden lg:flex flex-shrink-0 items-center space-x-4">
          <button
            onClick={handleSignUp}
            className="text-[#000000] dark:text-white hover:text-[#563D39] transition font-medium"
          >
            Sign Up
          </button>

          <button
            onClick={handleRedirect}
            className="bg-[#563D39] font-semibold text-white px-4 py-2 rounded-[6px] hover:bg-[#4a332f] transition-colors flex items-center justify-around space-x-2 w-[116px] h-[42px] pt-[6px] pr-[6px] pb-[6px] pl-[20px]"
          >
            <span>Login</span>
            <img
              src={leftArrow}
              alt="Left Arrow"
              className="w-[30px] h-[30px]"
            />
          </button>
        </div>

        {/* Mobile Navigation - Right */}
        <div className="lg:hidden flex flex-1 justify-end">
          {/* Mobile Menu Toggle Button */}
          <button
            onClick={() => setToggle(!toggle)}
            className="focus:outline-none"
            aria-label={toggle ? "Close Menu" : "Open Menu"}
          >
            <img
              src={toggle ? Close : Hamburger}
              alt="Menu Toggle"
              className="w-9 h-9"
            />
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {toggle && (
        <div className="lg:hidden bg-white dark:bg-dark[#939393] shadow-md py-4 px-4 space-y-4">
          {/* Sign Up and Login Buttons for Mobile */}
          <div className="pb-2 border-b border-gray-200 dark:border-gray-700 space-y-2">
            <button
              onClick={() => {
                handleSignUp();
                setToggle(false);
              }}
              className="w-full text-[#939393] dark:text-white hover:text-[#563D39]  transition font-medium py-2 text-center"
            >
              Sign Up
            </button>
            <button
              className="w-full bg-[#563D39] font-semibold rounded-lg px-4 py-2 flex items-center justify-center hover:bg-[#4a332f] transition-colors space-x-2"
              onClick={() => {
                handleRedirect();
                setToggle(false);
              }}
            >
              <span className="text-base font-medium text-white">Login</span>
              <svg
                className="w-4 h-4 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>

          <Link
            to="/"
            className={`block hover:text-[#563D39] font-semibold transition text-[#939393] dark:text-white py-2${currentPath === "/" ? " !text-[#563D39] font-semibold" : ""}`}
            onClick={() => {
              setToggle(false);
              scrollToTop();
            }}
          >
            Home
          </Link>

          <Link
            as="button"
            to="#"
            className={`block hover:text-[#563D39] font-semibold transition text-[#939393] dark:text-white py-2${currentPath === "/solutions" ? " !text-[#563D39] font-semibold" : ""}`}
            onClick={e => {
              e.preventDefault();
              scrollToSolutions();
            }}
          >
            Solutions
          </Link>

          <Link
            to="/blogs"
            className={`block hover:text-[#563D39] font-semibold transition text-[#939393] dark:text-white py-2${currentPath === "/blogs" ? " !text-[#563D39] font-semibold" : ""}`}
            onClick={() => {
              setToggle(false);
              scrollToTop();
            }}
          >
            Blogs
          </Link>

          <Link
            to="/aboutus"
            className={`block hover:text-[#563D39] font-semibold transition text-[#939393] dark:text-white py-2${currentPath === "/aboutus" ? " !text-[#563D39] font-semibold" : ""}`}
            onClick={() => {
              setToggle(false);
              scrollToTop();
            }}
          >
           About Us
          </Link>

          <Link
            to="/contact-us"
            className={`block hover:text-[#563D39] font-semibold transition text-[#939393] dark:text-white py-2${currentPath === "/contact-us" ? " !text-[#563D39] font-semibold" : ""}`}
            onClick={() => {
              setToggle(false);
              scrollToTop();
            }}
          >
            Contact Us
          </Link>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
