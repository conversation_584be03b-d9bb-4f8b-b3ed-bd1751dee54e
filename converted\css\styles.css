/* Base Styles */
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Figtree', sans-serif !important;
  overflow-x: hidden;
  width: 100%;
}

/* Custom Variables to match the original design */
:root {
  --primary: #563D39;
  --primary-light: rgba(86, 61, 57, 0.1);
  --primary-hover: #95725D;
  --text-muted: #939393;
  --text-dark: #000000;
  --text-light: #FFFFFF;
  --text-light-muted: rgba(255, 255, 255, 0.6);
}

/* Override Bootstrap's primary color */
.btn-primary {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

.btn-primary:hover {
  background-color: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
}

.bg-primary {
  background-color: var(--primary) !important;
}

.text-primary {
  color: var(--primary) !important;
}

/* Navbar Styles */
.navbar {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.navbar .nav-link {
  color: var(--text-muted);
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: color 0.3s;
}

.navbar .nav-link:hover,
.navbar .nav-link.active {
  color: var(--primary);
  font-weight: 600;
}

.bg-light-gradient {
  background: linear-gradient(to right, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.05));
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding-top: 80px; /* Account for fixed navbar */
}

/* Custom Button Styles */
.btn {
  border-radius: 30px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.15rem;
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: all 0.25s;
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  border-radius: 30px;
  background-color: var(--primary-hover);
  z-index: -1;
  transition: all 0.5s;
}

.btn-primary:hover::before {
  width: 100%;
}

/* Info Card Section */
.info-card {
  background-color: var(--primary);
  border-radius: 40px;
  color: var(--text-light);
}

.info-card .feature-icon {
  width: 70px;
  height: 70px;
}

.info-card .feature-title {
  color: var(--text-light);
  font-weight: 600;
}

.info-card .feature-description {
  color: var(--text-light-muted);
}

/* About Us Section */
.about-section {
  background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('../../public/background4.png');
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  color: var(--text-light);
}

/* Footer */
footer {
  font-family: 'Figtree', sans-serif;
}

footer a:hover {
  color: var(--primary) !important;
  text-decoration: underline !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .hero-section {
    text-align: center;
  }
  
  .btn {
    margin: 0.25rem;
  }
}

/* Animation for scrolling images */
@keyframes scroll-left {
  0% { transform: translateX(0); }
  100% { transform: translateX(-100%); }
}

@keyframes scroll-right {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(0); }
}

.scroll-container {
  overflow: hidden;
  width: 100%;
}

.scroll-left {
  animation: scroll-left 20s linear infinite;
  display: flex;
  width: max-content;
}

.scroll-right {
  animation: scroll-right 20s linear infinite;
  display: flex;
  width: max-content;
}

/* Blog Styles */
.blog-hero-image {
  height: 476px;
  background-size: cover;
  background-position: center;
  background-color: #f5f5f5;
}

.blog-content {
  max-width: 800px;
  line-height: 1.8;
}

.blog-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1.5rem 0;
}

.blog-content h1, .blog-content h2, .blog-content h3 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.blog-content p {
  margin-bottom: 1.5rem;
}

.blog-content ul, .blog-content ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.blog-content blockquote {
  border-left: 4px solid var(--primary);
  padding-left: 1rem;
  font-style: italic;
  margin: 1.5rem 0;
}

.social-icon {
  width: 32px;
  height: 32px;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.social-icon:hover {
  background-color: #e0e0e0;
}

.more-insights-section {
  border-top-left-radius: 40px;
  border-bottom-left-radius: 40px;
  margin-left: 5vw;
  margin-right: 0;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.w-90 {
  width: 90%;
}

.blog-card {
  cursor: pointer;
  transition: transform 0.3s;
}

.blog-card:hover {
  transform: translateY(-5px);
}

.w-32px { width: 32px; }
.h-32px { height: 32px; }
.bg-white-10 { background-color: rgba(255, 255, 255, 0.1); }
.hover-bg-white-20:hover { background-color: rgba(255, 255, 255, 0.2); }
.hover-white:hover { color: white !important; }
.text-white-50 { color: rgba(255, 255, 255, 0.5); }
.bg-white-opacity-25 { background-color: rgba(255, 255, 255, 0.25); }

/* Hero Images */
.hero-image {
  border-radius: 16px;
  object-fit: cover;
  margin-bottom: 1rem;
}

.hero-image-round {
  border-radius: 50%;
  object-fit: cover;
}

/* Mobile Image Carousel */
.mobile-image-carousel {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  gap: 1rem;
  padding: 1rem 0;
}

.mobile-image-carousel img {
  scroll-snap-align: center;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
}

/* Management Features */
.feature-card {
  background-color: #fff;
  border-radius: 20px;
  padding: 1.5rem;
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-card-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 1rem;
}

/* Testimonials */
.testimonial-card {
  background-color: #fff;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

/* FAQ Section */
.accordion-button:not(.collapsed) {
  background-color: var(--primary-light);
  color: var(--primary);
}

.accordion-button:focus {
  box-shadow: 0 0 0 0.25rem rgba(86, 61, 57, 0.25);
}