import React, { useState } from "react";
import {
    BeakerIcon,
    ClipboardIcon,
    RocketLaunchIcon,
    ChatBubbleOvalLeftIcon,
    DocumentMagnifyingGlassIcon,
    AdjustmentsHorizontalIcon,
} from "@heroicons/react/24/outline";
import { useTranslation } from "react-i18next";
import i18next from "i18next";

const Accordion = () => {
    const [activeIndex, setActiveIndex] = useState(null);
    const { t } = useTranslation();

    const toggleAccordion = (index) => {
        setActiveIndex(activeIndex === index ? null : index);
    };

    const handleLanguageChange = (lang) => {
        i18next.changeLanguage(lang);
    };

    const data = [
        {
            title: t("accordion.question1"),
            content: t("accordion.answer1"),
            icon: <BeakerIcon className="w-8 h-8 text-red-500" aria-hidden="true" />,
        },
        {
            title: t("accordion.question3"),
            content: t("accordion.answer3"),
            icon: <RocketLaunchIcon className="w-8 h-8 text-purple-500" aria-hidden="true" />,
        },

        {
            title: t("accordion.question5"),
            content: t("accordion.answer5"),
            icon: <ChatBubbleOvalLeftIcon className="w-8 h-8 text-blue-500" aria-hidden="true" />,
        },
    ];

    return (
        <div className="max-w-5xl mx-auto mt-10 p-6 sm:px-4">

            <div className="w-full bg-gray-200 text-black text-center py-6 mb-6 rounded-lg shadow-md dark:bg-gray-800 dark:text-white">
                <h1 className="text-3xl font-bold">{t('accordion.title')}</h1>
            </div>

            {data.map((item, index) => (
                <div
                    key={index}
                    className={`border rounded-lg mb-4 shadow-md transition-all ${activeIndex === index ? "border-gray-400" : "border-gray-300"
                        } dark:bg-gray-900 dark:text-white`}
                >
                    <button
                        aria-expanded={activeIndex === index}
                        className="w-full flex justify-between items-center px-6 py-4 bg-gray-50 hover:bg-gray-100 rounded-lg dark:bg-gray-700 dark:hover:bg-gray-600"
                        onClick={() => toggleAccordion(index)}
                    >
                        <div className="flex items-center gap-4">
                            {item.icon}
                            <span className="text-lg font-medium text-gray-800 dark:text-white">
                                {item.title}
                            </span>
                        </div>
                        <span
                            className={`transform transition-all duration-300 ${activeIndex === index ? "rotate-180" : "rotate-0"
                                } dark:text-white`}
                        >
                            {activeIndex === index ? "▲" : "▼"}
                        </span>
                    </button>
                    <div
                        className={`overflow-hidden transition-all duration-500 ease-out ${activeIndex === index ? "max-h-screen opacity-100" : "max-h-0 opacity-0"
                            }`}
                        aria-hidden={activeIndex !== index}
                    >
                        <div className="p-6 bg-gray-100 dark:bg-gray-800">
                            <p className="text-gray-800 dark:text-gray-300">{item.content}</p>
                        </div>
                    </div>
                </div>
            ))}


        </div>
    );
};

export default Accordion;
