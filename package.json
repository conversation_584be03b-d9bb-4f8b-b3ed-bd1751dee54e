{"name": "cfaw-flowkar-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:analyze": "vite build --mode analyze", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.1.1", "@mui/styles": "^6.4.12", "@rollup/rollup-win32-x64-msvc": "^4.46.2", "axios": "^1.10.0", "framer-motion": "^12.18.1", "gsap": "^3.13.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.517.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.3", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "swiper": "^11.2.8", "web-vitals": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "vite": "^6.3.5"}}