<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Resource Hints for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="dns-prefetch" href="https://api.flowkar.com">
    
    <!-- Performance Optimization -->
    <meta name="theme-color" content="#563D39">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <!-- Cache Control Headers -->
    <meta http-equiv="Cache-Control" content="public, max-age=31536000">
    
    <title>Contact Us - Flowkar | Get in Touch</title>
    <meta name="description" content="Get in touch with the Flowkar team. We're here to help with any questions about our social media management platform.">
    
    <!-- Bootstrap 5.3.x CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- Google Fonts - Figtree -->
    <link href="https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="src/assets/logo.svg">
    <link rel="preload" href="src/assets/logo.svg" as="image" type="image/svg+xml">
</head>
<body>
    <!-- Navigation -->
    <nav id="navbar-placeholder"></nav>
    
    <!-- Main Content -->
    <main style="padding-top: 80px;">
        <!-- Hero Section -->
        <section class="py-5 bg-light">
            <div class="container-fluid" style="max-width: 1200px;">
                <div class="text-center mb-5">
                    <h1 class="display-3 fw-bold mb-4" style="color: var(--primary-brown);">Contact Us</h1>
                    <p class="fs-4 text-muted">We're here to help you succeed with Flowkar</p>
                </div>
            </div>
        </section>
        
        <!-- Contact Form Section -->
        <section class="py-5">
            <div class="container-fluid" style="max-width: 1000px;">
                <div class="row g-5">
                    <!-- Contact Form -->
                    <div class="col-12 col-lg-8">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body p-5">
                                <h2 class="h3 fw-bold mb-4" style="color: var(--primary-brown);">Send us a message</h2>
                                <form id="contactForm">
                                    <div class="row g-3">
                                        <div class="col-12 col-md-6">
                                            <label for="firstName" class="form-label fw-semibold">First Name *</label>
                                            <input type="text" class="form-control form-control-lg" id="firstName" required>
                                        </div>
                                        <div class="col-12 col-md-6">
                                            <label for="lastName" class="form-label fw-semibold">Last Name *</label>
                                            <input type="text" class="form-control form-control-lg" id="lastName" required>
                                        </div>
                                        <div class="col-12">
                                            <label for="email" class="form-label fw-semibold">Email Address *</label>
                                            <input type="email" class="form-control form-control-lg" id="email" required>
                                        </div>
                                        <div class="col-12">
                                            <label for="company" class="form-label fw-semibold">Company/Organization</label>
                                            <input type="text" class="form-control form-control-lg" id="company">
                                        </div>
                                        <div class="col-12">
                                            <label for="subject" class="form-label fw-semibold">Subject *</label>
                                            <select class="form-select form-select-lg" id="subject" required>
                                                <option value="">Select a subject</option>
                                                <option value="general">General Inquiry</option>
                                                <option value="support">Technical Support</option>
                                                <option value="sales">Sales Question</option>
                                                <option value="partnership">Partnership Opportunity</option>
                                                <option value="feedback">Feedback</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <label for="message" class="form-label fw-semibold">Message *</label>
                                            <textarea class="form-control" id="message" rows="5" required placeholder="Tell us how we can help you..."></textarea>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="newsletter">
                                                <label class="form-check-label" for="newsletter">
                                                    I'd like to receive updates about Flowkar's new features and tips
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-lg px-5 py-3 fw-bold" style="background-color: var(--primary-brown); color: white; border: none;">
                                                Send Message
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact Information -->
                    <div class="col-12 col-lg-4">
                        <div class="h-100">
                            <h3 class="h4 fw-bold mb-4" style="color: var(--primary-brown);">Get in Touch</h3>
                            
                            <div class="mb-4">
                                <div class="d-flex align-items-start mb-3">
                                    <div class="me-3">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: var(--primary-brown) !important;">
                                            <i class="fas fa-envelope text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h5 class="fw-semibold mb-1">Email</h5>
                                        <p class="text-muted mb-0">
                                            <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="d-flex align-items-start mb-3">
                                    <div class="me-3">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: var(--primary-brown) !important;">
                                            <i class="fas fa-clock text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h5 class="fw-semibold mb-1">Response Time</h5>
                                        <p class="text-muted mb-0">We typically respond within 24 hours</p>
                                    </div>
                                </div>
                                
                                <div class="d-flex align-items-start mb-3">
                                    <div class="me-3">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: var(--primary-brown) !important;">
                                            <i class="fas fa-headset text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h5 class="fw-semibold mb-1">Support</h5>
                                        <p class="text-muted mb-0">24/7 support for all users</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, var(--primary-brown) 0%, var(--primary-brown-hover) 100%);">
                                <div class="card-body p-4 text-white text-center">
                                    <h5 class="fw-bold mb-3">Need Immediate Help?</h5>
                                    <p class="mb-3 opacity-75">Check out our comprehensive help center for instant answers to common questions.</p>
                                    <button class="btn btn-light fw-semibold" onclick="window.open('https://help.flowkar.com', '_blank')">
                                        Visit Help Center
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- FAQ Section -->
        <section class="py-5 bg-light">
            <div class="container-fluid" style="max-width: 800px;">
                <div class="text-center mb-5">
                    <h2 class="display-5 fw-bold mb-3" style="color: var(--primary-brown);">Common Questions</h2>
                    <p class="fs-5 text-muted">Quick answers to help you get started</p>
                </div>
                
                <div class="accordion" id="contactFaqAccordion">
                    <div class="accordion-item border-0 mb-3">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#faq1" style="background: white; color: var(--primary-brown);">
                                How quickly can I get started with Flowkar?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#contactFaqAccordion">
                            <div class="accordion-body text-muted">
                                You can get started immediately! Simply sign up for a free trial and you'll have access to all of Flowkar's features within minutes.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item border-0 mb-3">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#faq2" style="background: white; color: var(--primary-brown);">
                                Do you offer training or onboarding?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#contactFaqAccordion">
                            <div class="accordion-body text-muted">
                                Yes! We provide comprehensive onboarding for all new users, including video tutorials, documentation, and personalized support sessions for enterprise customers.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item border-0 mb-3">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#faq3" style="background: white; color: var(--primary-brown);">
                                Can I migrate my existing content and data?
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#contactFaqAccordion">
                            <div class="accordion-body text-muted">
                                Absolutely! Our team can help you migrate your existing content, scheduled posts, and analytics data from other platforms. Contact us for a personalized migration plan.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer id="footer-placeholder"></footer>
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Bootstrap 5.3.x JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/animations.js"></script>
    
    <script>
        // Initialize page-specific content
        document.addEventListener('DOMContentLoaded', function() {
            loadNavbar();
            loadFooter();
            initializeScrollAnimations();
            
            // Handle contact form submission
            document.getElementById('contactForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form data
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                
                // Show success message (in a real app, you'd send this to your backend)
                alert('Thank you for your message! We\'ll get back to you within 24 hours.');
                
                // Reset form
                this.reset();
            });
        });
    </script>
</body>
</html>
