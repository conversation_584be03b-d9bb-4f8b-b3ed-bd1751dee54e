/**
 * Service Worker for Advanced Asset Caching
 * Implements intelligent caching strategies for different asset types
 */

const CACHE_NAME = 'flowkar-cache-v1';
const STATIC_CACHE = 'flowkar-static-v1';
const IMAGE_CACHE = 'flowkar-images-v1';
const API_CACHE = 'flowkar-api-v1';

// Cache strategies for different asset types
const CACHE_STRATEGIES = {
  // Static assets (JS, CSS) - Cache first with network fallback
  static: 'cache-first',
  // Images - Cache first with stale-while-revalidate
  images: 'stale-while-revalidate',
  // API calls - Network first with cache fallback
  api: 'network-first',
  // HTML - Network first
  html: 'network-first'
};

// Assets to cache immediately on install
const CRITICAL_ASSETS = [
  '/',
  '/static/js/main.js',
  '/static/css/main.css',
  '/manifest.json'
];

// Maximum cache sizes
const MAX_CACHE_SIZES = {
  [STATIC_CACHE]: 50,
  [IMAGE_CACHE]: 100,
  [API_CACHE]: 50
};

// Cache duration (in milliseconds)
const CACHE_DURATION = {
  static: 7 * 24 * 60 * 60 * 1000, // 7 days
  images: 30 * 24 * 60 * 60 * 1000, // 30 days
  api: 5 * 60 * 1000 // 5 minutes
};

/**
 * Install event - Cache critical assets
 */
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Caching critical assets...');
        return cache.addAll(CRITICAL_ASSETS);
      })
      .then(() => {
        console.log('Critical assets cached successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Failed to cache critical assets:', error);
      })
  );
});

/**
 * Activate event - Clean up old caches
 */
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && 
                cacheName !== STATIC_CACHE && 
                cacheName !== IMAGE_CACHE && 
                cacheName !== API_CACHE) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker activated');
        return self.clients.claim();
      })
  );
});

/**
 * Fetch event - Implement caching strategies
 */
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }

  event.respondWith(handleRequest(request));
});

/**
 * Handle different types of requests with appropriate caching strategies
 */
async function handleRequest(request) {
  const url = new URL(request.url);
  
  try {
    // Determine asset type and cache strategy
    if (isImageRequest(request)) {
      return handleImageRequest(request);
    } else if (isStaticAsset(request)) {
      return handleStaticAsset(request);
    } else if (isAPIRequest(request)) {
      return handleAPIRequest(request);
    } else {
      return handleHTMLRequest(request);
    }
  } catch (error) {
    console.error('Error handling request:', error);
    return fetch(request);
  }
}

/**
 * Handle image requests with stale-while-revalidate strategy
 */
async function handleImageRequest(request) {
  const cache = await caches.open(IMAGE_CACHE);
  const cachedResponse = await cache.match(request);
  
  // Return cached version immediately if available
  if (cachedResponse && !isExpired(cachedResponse, 'images')) {
    // Fetch fresh version in background
    fetchAndCache(request, cache);
    return cachedResponse;
  }
  
  // Fetch fresh version
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
      await manageCacheSize(IMAGE_CACHE);
    }
    return networkResponse;
  } catch (error) {
    // Return stale cache if network fails
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

/**
 * Handle static assets with cache-first strategy
 */
async function handleStaticAsset(request) {
  const cache = await caches.open(STATIC_CACHE);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse && !isExpired(cachedResponse, 'static')) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
      await manageCacheSize(STATIC_CACHE);
    }
    return networkResponse;
  } catch (error) {
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

/**
 * Handle API requests with network-first strategy
 */
async function handleAPIRequest(request) {
  const cache = await caches.open(API_CACHE);
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
      await manageCacheSize(API_CACHE);
    }
    return networkResponse;
  } catch (error) {
    const cachedResponse = await cache.match(request);
    if (cachedResponse && !isExpired(cachedResponse, 'api')) {
      return cachedResponse;
    }
    throw error;
  }
}

/**
 * Handle HTML requests with network-first strategy
 */
async function handleHTMLRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match('/');
    return cachedResponse || new Response('Offline', { status: 503 });
  }
}

/**
 * Utility functions
 */
function isImageRequest(request) {
  return /\.(png|jpg|jpeg|gif|svg|webp|avif)$/i.test(request.url);
}

function isStaticAsset(request) {
  return /\.(js|css|woff|woff2|ttf|eot)$/i.test(request.url);
}

function isAPIRequest(request) {
  return request.url.includes('/api/') || request.url.includes('api.');
}

function isExpired(response, type) {
  const dateHeader = response.headers.get('date');
  if (!dateHeader) return false;
  
  const responseDate = new Date(dateHeader);
  const now = new Date();
  const age = now.getTime() - responseDate.getTime();
  
  return age > CACHE_DURATION[type];
}

async function fetchAndCache(request, cache) {
  try {
    const response = await fetch(request);
    if (response.ok) {
      await cache.put(request, response.clone());
    }
  } catch (error) {
    console.warn('Background fetch failed:', error);
  }
}

async function manageCacheSize(cacheName) {
  const cache = await caches.open(cacheName);
  const keys = await cache.keys();
  const maxSize = MAX_CACHE_SIZES[cacheName];
  
  if (keys.length > maxSize) {
    const keysToDelete = keys.slice(0, keys.length - maxSize);
    await Promise.all(keysToDelete.map(key => cache.delete(key)));
  }
}

/**
 * Message handling for cache management
 */
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    clearAllCaches();
  }
});

async function clearAllCaches() {
  const cacheNames = await caches.keys();
  await Promise.all(cacheNames.map(name => caches.delete(name)));
  console.log('All caches cleared');
}
