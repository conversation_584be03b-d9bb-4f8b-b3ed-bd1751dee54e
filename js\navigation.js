// Navigation functionality for Flowkar website

// Navigation functions
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
    updateActiveNavLink('home');
}

function scrollToSolutions() {
    const solutionsSection = document.getElementById('management-features');
    if (solutionsSection) {
        const yOffset = -100; // Offset for navbar height
        const y = solutionsSection.getBoundingClientRect().top + window.pageYOffset + yOffset;
        window.scrollTo({
            top: y,
            behavior: 'smooth'
        });
        updateActiveNavLink('solutions');
    }
}

function closeMenu() {
    const mobileMenu = document.getElementById('mobileMenu');
    if (mobileMenu) {
        const bsCollapse = new bootstrap.Collapse(mobileMenu, {
            toggle: false
        });
        bsCollapse.hide();
    }
    
    // Update menu icon
    const menuIcon = document.getElementById('menu-icon');
    if (menuIcon) {
        menuIcon.src = 'src/assets/hamburgerMenu.svg';
    }
}

function updateActiveNavLink(section) {
    // Remove active class from all nav links
    document.querySelectorAll('.nav-link-custom').forEach(link => {
        link.classList.remove('active');
    });
    
    // Add active class to current section
    const activeLink = document.querySelector(`[href="#${section}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

// Handle authentication redirects
function handleSignUp() {
    window.location.href = "https://app.flowkar.com/sign-up";
}

function handleLogin() {
    const userdataString = localStorage.getItem("UserId");
    if (userdataString) {
        try {
            const userdata = JSON.parse(userdataString);
            if (userdata) {
                window.location.href = "https://app.flowkar.com/dashboard";
            } else {
                window.location.href = "https://app.flowkar.com/sign-in";
            }
        } catch (error) {
            console.error("Error parsing user data:", error);
            window.location.href = "https://app.flowkar.com/sign-in";
        }
    } else {
        window.location.href = "https://app.flowkar.com/sign-in";
    }
}

// Mobile menu toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    // Handle mobile menu toggle icon change
    const mobileMenuToggle = document.querySelector('.navbar-toggler');
    const mobileMenu = document.getElementById('mobileMenu');
    const menuIcon = document.getElementById('menu-icon');
    
    if (mobileMenu && menuIcon) {
        mobileMenu.addEventListener('show.bs.collapse', function() {
            menuIcon.src = 'src/assets/close.svg';
        });
        
        mobileMenu.addEventListener('hide.bs.collapse', function() {
            menuIcon.src = 'src/assets/hamburgerMenu.svg';
        });
    }
    
    // Handle scroll-based navigation highlighting
    window.addEventListener('scroll', handleScrollNavigation);
});

// Handle scroll-based navigation highlighting
function handleScrollNavigation() {
    const sections = [
        { id: 'hero-section', nav: 'home' },
        { id: 'management-features', nav: 'solutions' },
        { id: 'social-media-icons', nav: 'social' },
        { id: 'info-card', nav: 'info' },
        { id: 'about-us-hero', nav: 'about' },
        { id: 'thoughts', nav: 'thoughts' },
        { id: 'testimonials', nav: 'testimonials' },
        { id: 'text-reveal', nav: 'text' },
        { id: 'faq', nav: 'faq' }
    ];
    
    const scrollPosition = window.scrollY + 150; // Offset for navbar
    
    for (let i = sections.length - 1; i >= 0; i--) {
        const section = document.getElementById(sections[i].id);
        if (section && scrollPosition >= section.offsetTop) {
            updateActiveNavLink(sections[i].nav);
            break;
        }
    }
}

// Page navigation functions
function navigateToPage(page) {
    switch(page) {
        case 'home':
            window.location.href = 'index.html';
            break;
        case 'about':
            window.location.href = 'about.html';
            break;
        case 'solutions':
            window.location.href = 'solutions.html';
            break;
        case 'blogs':
            window.location.href = 'blogs.html';
            break;
        case 'contact':
            window.location.href = 'contact.html';
            break;
        case 'terms':
            window.location.href = 'terms.html';
            break;
        case 'privacy':
            window.location.href = 'privacy.html';
            break;
        default:
            console.warn('Unknown page:', page);
    }
}

// Smooth scroll to element with offset
function scrollToElement(elementId, offset = -100) {
    const element = document.getElementById(elementId);
    if (element) {
        const y = element.getBoundingClientRect().top + window.pageYOffset + offset;
        window.scrollTo({
            top: y,
            behavior: 'smooth'
        });
    }
}

// Handle browser back/forward navigation
window.addEventListener('popstate', function(event) {
    if (event.state && event.state.page) {
        loadPage(event.state.page);
    }
});

// Add page to browser history
function addToHistory(page, title) {
    const state = { page: page };
    history.pushState(state, title, `#${page}`);
}

// Preload images for better performance
function preloadImages(imageUrls) {
    imageUrls.forEach(url => {
        const img = new Image();
        img.src = url;
    });
}

// Initialize navigation on page load
document.addEventListener('DOMContentLoaded', function() {
    // Preload critical images
    const criticalImages = [
        'src/assets/logo.svg',
        'src/assets/hamburgerMenu.svg',
        'src/assets/close.svg',
        'src/assets/Navbar/leftArrow.svg'
    ];
    preloadImages(criticalImages);
    
    // Set initial active nav state based on current page
    const currentPath = window.location.pathname;
    if (currentPath.includes('about')) {
        updateActiveNavLink('about');
    } else if (currentPath.includes('solutions')) {
        updateActiveNavLink('solutions');
    } else if (currentPath.includes('blogs')) {
        updateActiveNavLink('blogs');
    } else if (currentPath.includes('contact')) {
        updateActiveNavLink('contact');
    } else {
        updateActiveNavLink('home');
    }
});

// Utility function to debounce scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Debounced scroll handler
const debouncedScrollHandler = debounce(handleScrollNavigation, 10);
window.addEventListener('scroll', debouncedScrollHandler);

// Handle keyboard navigation
document.addEventListener('keydown', function(event) {
    // Handle Escape key to close mobile menu
    if (event.key === 'Escape') {
        closeMenu();
    }
    
    // Handle Enter key on focusable elements
    if (event.key === 'Enter') {
        const focusedElement = document.activeElement;
        if (focusedElement && focusedElement.classList.contains('nav-link-custom')) {
            focusedElement.click();
        }
    }
});

// Accessibility improvements
function improveAccessibility() {
    // Add ARIA labels to navigation elements
    const navLinks = document.querySelectorAll('.nav-link-custom');
    navLinks.forEach(link => {
        if (!link.getAttribute('aria-label')) {
            const text = link.textContent.trim();
            link.setAttribute('aria-label', `Navigate to ${text}`);
        }
    });
    
    // Add focus indicators
    const focusableElements = document.querySelectorAll('a, button, [tabindex]');
    focusableElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.style.outline = '2px solid #563D39';
            this.style.outlineOffset = '2px';
        });
        
        element.addEventListener('blur', function() {
            this.style.outline = '';
            this.style.outlineOffset = '';
        });
    });
}

// Initialize accessibility improvements
document.addEventListener('DOMContentLoaded', improveAccessibility);
