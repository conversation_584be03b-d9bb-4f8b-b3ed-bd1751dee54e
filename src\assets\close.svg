<svg width="52" height="58" viewBox="0 0 52 58" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_865_144)">
<rect x="4" y="4" width="44" height="50" rx="5" fill="white" shape-rendering="crispEdges"/>
<path d="M36 39L26 29M26 29L16 19M26 29L36 19M26 29L16 39" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_d_865_144" x="0" y="0" width="52" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_865_144"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_865_144" result="shape"/>
</filter>
</defs>
</svg>
