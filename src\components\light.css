/* light.css */

/* Light theme defaults */
:root {
    --background-color: #ffffff;
    --text-color: #000000;

  }
  
  /* Dark theme overrides */
  .dark {
    --background-color: #151515; /* Example dark background */
    --text-color: #f3f4f6; /* Example dark text color */
   
  }
  
  body {
    background-color: var(--background-color);
    color: var(--text-color);
  }
  
  a {
    color: var(--text-color);
  }
  
  button {
    background-color: var(--primary-color);
  }
  
  /* light.css or a dedicated CSS file */
#tsparticles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: -1;
}

  