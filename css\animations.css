/* Animation Keyframes and Classes */

/* Horizontal Scroll Animations */
@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Default animation for larger screens */
.animate-scroll {
    animation: scroll 20s linear infinite;
}

/* Increase animation speed for mobile screens (≤ 640px) */
@media (max-width: 640px) {
    .animate-scroll {
        animation: scroll 10s linear infinite;
    }
}

/* Optionally, for other breakpoints, you can set different speeds */
@media (max-width: 768px) {
    .animate-scroll {
        animation: scroll 10s linear infinite;
    }
}

@media (max-width: 320px) {
    .animate-scroll {
        animation: scroll 5s linear infinite;
    }
}

/* Infinite vertical scrolling animations for hero section columns */
@keyframes scrollUp {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-50%);
    }
}

@keyframes scrollDown {
    0% {
        transform: translateY(-50%);
    }
    100% {
        transform: translateY(0);
    }
}

.animate-scroll-up {
    animation: scrollUp 20s linear infinite;
}

.animate-scroll-down {
    animation: scrollDown 20s linear infinite;
}

/* Responsive animation speeds for vertical scrolling */
@media (max-width: 768px) {
    .animate-scroll-up {
        animation: scrollUp 15s linear infinite;
    }

    .animate-scroll-down {
        animation: scrollDown 15s linear infinite;
    }
}

@media (max-width: 640px) {
    .animate-scroll-up {
        animation: scrollUp 12s linear infinite;
    }

    .animate-scroll-down {
        animation: scrollDown 12s linear infinite;
    }
}

/* Horizontal Left Scroll - Updated for triple images */
@keyframes scrollLeft {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-33.333%);
    }
}

/* Horizontal Right Scroll - Updated for triple images */
@keyframes scrollRight {
    0% {
        transform: translateX(-33.333%);
    }
    100% {
        transform: translateX(0);
    }
}

/* Default animation speeds - Optimized for infinite scroll */
.animate-scroll-left {
    animation: scrollLeft 25s linear infinite;
}

.animate-scroll-right {
    animation: scrollRight 25s linear infinite;
}

/* Responsive Speeds - Consistent infinite scrolling */
@media (max-width: 768px) {
    .animate-scroll-left {
        animation: scrollLeft 20s linear infinite;
    }
    .animate-scroll-right {
        animation: scrollRight 20s linear infinite;
    }
}

@media (max-width: 640px) {
    .animate-scroll-left {
        animation: scrollLeft 15s linear infinite;
    }
    .animate-scroll-right {
        animation: scrollRight 15s linear infinite;
    }
}

@media (max-width: 320px) {
    .animate-scroll-left {
        animation: scrollLeft 12s linear infinite;
    }
    .animate-scroll-right {
        animation: scrollRight 12s linear infinite;
    }
}

/* Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* Slide In Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

/* Scale Animation */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.scale-in {
    animation: scaleIn 0.5s ease-out;
}

/* Pulse Animation */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

.bounce {
    animation: bounce 1s;
}

/* Skeleton Loading Animation */
@keyframes skeleton-loading {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: skeleton-loading 1.5s infinite;
}

/* Transform GPU Acceleration */
.transform-gpu {
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: transform;
}

/* Smooth Transitions */
.transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-fast {
    transition: all 0.15s ease-in-out;
}

.transition-slow {
    transition: all 0.6s ease-in-out;
}

/* Hover Effects */
.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Loading Spinner */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.spinner {
    animation: spin 1s linear infinite;
}

/* Intersection Observer Animation Classes */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease-out;
}

.animate-on-scroll.in-view {
    opacity: 1;
    transform: translateY(0);
}
