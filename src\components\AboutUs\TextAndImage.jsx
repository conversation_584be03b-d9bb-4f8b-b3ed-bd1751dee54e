import React from "react";
import Image_01 from "../../assets/AboutUs/Image_01.svg";

function TextAndImage() {
  return (
    <div className="bg-[#5C4742] rounded-[40px] flex flex-col md:flex-row items-center justify-center p-6 md:p-12 w-[90%] mx-auto my-8 min-h-[400px] font-figtree">
      {/* Left: Image */}
      <div className="flex-shrink-0 w-full md:w-1/2 flex justify-center items-center mb-8 md:mb-0 md:mr-8">
        <img
          src={Image_01}
          alt="Trusted by creators"
          className="rounded-[30px] object-cover w-[320px] h-[320px] md:w-[642px] md:h-[514px] shadow-lg"
        />
      </div>
      {/* Right: Text */}
      <div className="w-full md:w-1/2 text-white flex flex-col justify-center">
        <h2 className="text-3xl md:text-5xl font-nromal mb-6 leading-tight">
          Trusted By Creators, Built For Growth
        </h2>
        <p className="text-[#FFFFFFB2] text-lg md:text-xl mb-4 opacity-70">
          Flowkar is the all-in-one platform built for Creators, Influencers,
          Business Professionals and Teams who are done juggling tools. From
          scheduling and posting to deep analytics, everything lives in one
          place - faster, smarter and made for today’s content-driven world.
        </p>
        <p className="text-[#FFFFFFB2] text-lg md:text-xl opacity-70">
          Whether you're managing one brand or a dozen, Flowkar adapts to your
          flow - not the other way around. Trusted by thousands of creators
          globally, it helps you save time, reduce chaos and take full control
          of your growth.
        </p>
      </div>
    </div>
  );
}

export default TextAndImage;
