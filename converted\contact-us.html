<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <link rel="icon" type="image/svg+xml" href="../src/assets/logo.svg">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- Resource Hints for Performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="dns-prefetch" href="https://api.flowkar.com">

  <!-- Preload Critical Assets -->
  <link rel="preload" href="../src/assets/logo.svg" as="image" type="image/svg+xml">

  <!-- Cache Control Headers -->
  <meta http-equiv="Cache-Control" content="public, max-age=31536000">

  <!-- Performance Optimization -->
  <meta name="theme-color" content="#563D39">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">

  <title>Contact Us - Flowkar</title>
  <meta name="description" content="Get in touch with the Flowkar team for support, feedback, or partnership inquiries.">

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  
  <!-- Google Fonts - Figtree -->
  <link href="https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap" rel="stylesheet">
  
  <!-- Custom CSS -->
  <link rel="stylesheet" href="./css/styles.css">
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg fixed-top bg-white shadow-sm">
    <div class="container">
      <!-- Logo -->
      <a class="navbar-brand" href="index.html">
        <img src="../src/assets/logo.svg" alt="Logo" class="img-fluid" width="128">
      </a>
      
      <!-- Mobile Toggle Button -->
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      
      <!-- Navigation Links -->
      <div class="collapse navbar-collapse justify-content-center" id="navbarNav">
        <div class="navbar-nav bg-light-gradient rounded-pill px-4 py-2">
          <a class="nav-link" href="index.html">Home</a>
          <a class="nav-link" href="index.html#solutions-section">Solutions</a>
          <a class="nav-link" href="blogs.html">Blogs</a>
          <a class="nav-link" href="aboutus.html">About Us</a>
          <a class="nav-link active" href="contact-us.html">Contact Us</a>
        </div>
      </div>
      
      <!-- Sign Up and Login Buttons -->
      <div class="d-none d-lg-flex align-items-center gap-3">
        <button class="btn btn-link text-dark fw-medium" data-action="signup">
          Sign Up
        </button>
        <button class="btn btn-primary d-flex align-items-center gap-2 px-3 py-2" data-action="login">
          <span>Login</span>
          <img src="../src/assets/Navbar/leftArrow.svg" alt="Left Arrow" width="30" height="30">
        </button>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main>
    <!-- Hero Section -->
    <section class="mt-5 pt-5">
      <div class="container">
        <header class="bg-primary rounded-4 text-white p-4 p-sm-5 d-flex flex-column flex-md-row align-items-center justify-content-between position-relative overflow-hidden mt-4 mb-5">
          <!-- Background Image -->
          <div class="position-absolute top-0 start-0 w-100 h-100" style="background-image: url('../src/assets/Blog/Blog_bg.svg'); background-size: cover; background-position: center; opacity: 0.2;"></div>
          
          <!-- Left Side Content -->
          <div class="col-md-6 position-relative text-center text-md-start py-4 py-md-0">
            <h1 class="display-5 fw-bold mb-3">Contact Us</h1>
            <h2 class="h4 mb-4">We'd love to hear from you</h2>
            <p class="lead mb-0">Have questions or feedback? Our team is here to help!</p>
          </div>
          
          <!-- Right Side Image -->
          <div class="d-flex justify-content-center align-items-end position-relative mt-4 mt-md-0">
            <img src="../src/assets/ContactUs/Dummy_01.svg" alt="Contact Illustration" class="img-fluid" style="width: 260px; height: 320px; border-radius: 40px;">
            <!-- Circle Decoration -->
            <img src="../src/assets/ContactUs/Circle.svg" alt="" class="position-absolute" style="width: 120px; height: 120px; bottom: -20px; right: -30px; z-index: -1;">
          </div>
        </header>
      </div>
    </section>

    <!-- Contact Form Section -->
    <section class="py-5">
      <div class="container">
        <div class="row g-5">
          <!-- Left Side - Form -->
          <div class="col-lg-7">
            <h2 class="h3 fw-bold mb-4">Send us a message</h2>
            <form id="contactForm" class="needs-validation" novalidate>
              <div class="row g-3">
                <!-- Name Field -->
                <div class="col-md-6 mb-3">
                  <label for="name" class="form-label">Your Name</label>
                  <input type="text" class="form-control form-control-lg rounded-3" id="name" placeholder="Enter your name" required>
                  <div class="invalid-feedback">Please provide your name.</div>
                </div>
                
                <!-- Email Field -->
                <div class="col-md-6 mb-3">
                  <label for="email" class="form-label">Email Address</label>
                  <input type="email" class="form-control form-control-lg rounded-3" id="email" placeholder="Enter your email" required>
                  <div class="invalid-feedback">Please provide a valid email.</div>
                </div>
                
                <!-- Subject Field -->
                <div class="col-12 mb-3">
                  <label for="subject" class="form-label">Subject</label>
                  <input type="text" class="form-control form-control-lg rounded-3" id="subject" placeholder="Enter subject" required>
                  <div class="invalid-feedback">Please provide a subject.</div>
                </div>
                
                <!-- Message Field -->
                <div class="col-12 mb-4">
                  <label for="message" class="form-label">Message</label>
                  <textarea class="form-control form-control-lg rounded-3" id="message" rows="5" placeholder="Enter your message" required></textarea>
                  <div class="invalid-feedback">Please provide a message.</div>
                </div>
                
                <!-- Submit Button -->
                <div class="col-12">
                  <button type="submit" class="btn btn-primary btn-lg px-5">Send Message</button>
                </div>
              </div>
            </form>
          </div>
          
          <!-- Right Side - Contact Info -->
          <div class="col-lg-5">
            <h2 class="h3 fw-bold mb-4">Contact Information</h2>
            
            <!-- Contact Cards -->
            <div class="card border-0 shadow-sm rounded-4 mb-4">
              <div class="card-body p-4">
                <div class="d-flex align-items-center">
                  <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                    <img src="../src/assets/contact-us-home.svg" alt="Location Icon" width="24" height="24">
                  </div>
                  <div>
                    <h3 class="h6 fw-bold mb-1">Our Location</h3>
                    <p class="mb-0 text-muted">123 Social Media Street, Digital City, 10001</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="card border-0 shadow-sm rounded-4 mb-4">
              <div class="card-body p-4">
                <div class="d-flex align-items-center">
                  <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                    <img src="../src/assets/user-contact.svg" alt="Email Icon" width="24" height="24">
                  </div>
                  <div>
                    <h3 class="h6 fw-bold mb-1">Email Us</h3>
                    <p class="mb-0 text-muted"><EMAIL></p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="card border-0 shadow-sm rounded-4 mb-4">
              <div class="card-body p-4">
                <div class="d-flex align-items-center">
                  <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                    <img src="../src/assets/contact-like.svg" alt="Support Icon" width="24" height="24">
                  </div>
                  <div>
                    <h3 class="h6 fw-bold mb-1">Customer Support</h3>
                    <p class="mb-0 text-muted"><EMAIL></p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="card border-0 shadow-sm rounded-4">
              <div class="card-body p-4">
                <div class="d-flex align-items-center">
                  <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                    <img src="../src/assets/contact-inavation.svg" alt="Business Icon" width="24" height="24">
                  </div>
                  <div>
                    <h3 class="h6 fw-bold mb-1">Business Inquiries</h3>
                    <p class="mb-0 text-muted"><EMAIL></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
      <div class="container py-4">
        <div class="bg-primary text-white p-4 p-md-5 rounded-4">
          <h2 class="text-center h1 mb-5">Why Choose Flowkar Support?</h2>
          
          <div class="row g-4">
            <!-- Feature 1 -->
            <div class="col-md-4">
              <div class="p-4 h-100">
                <h3 class="h4 mb-3">Support That Puts You First</h3>
                <p class="mb-0">
                  At Flowkar, we're committed to making your journey smooth and rewarding. Whether you need help with features, face a technical hiccup, or just want guidance – our team is always here to support you at every step.
                </p>
              </div>
            </div>
            
            <!-- Feature 2 -->
            <div class="col-md-4">
              <div class="p-4 h-100">
                <h3 class="h4 mb-3">Driven by Innovation</h3>
                <p class="mb-0">
                  At Flowkar, we don't just follow trends — we set them. Our forward-thinking approach ensures you're always equipped with cutting-edge tools designed to elevate and simplify your social media experience.
                </p>
              </div>
            </div>
            
            <!-- Feature 3 -->
            <div class="col-md-4">
              <div class="p-4 h-100">
                <h3 class="h4 mb-3">Committed to Excellence</h3>
                <p class="mb-0">
                  We build Flowkar with precision and care, ensuring top-tier performance and dependable service. Your experience matters most, and we are dedicated to making every interaction outstanding.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-5 bg-light">
      <div class="container py-4">
        <h2 class="text-center mb-5">Frequently Asked Questions</h2>
        
        <div class="row justify-content-center">
          <div class="col-lg-8">
            <div class="accordion" id="contactFAQ">
              <!-- FAQ Item 1 -->
              <div class="accordion-item border-0 mb-3 rounded-3 shadow-sm">
                <h2 class="accordion-header">
                  <button class="accordion-button rounded-3" type="button" data-bs-toggle="collapse" data-bs-target="#faq1" aria-expanded="true" aria-controls="faq1">
                    How quickly can I expect a response to my inquiry?
                  </button>
                </h2>
                <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#contactFAQ">
                  <div class="accordion-body">
                    We typically respond to all inquiries within 24 hours during business days. For urgent matters, our premium support team is available with faster response times.
                  </div>
                </div>
              </div>
              
              <!-- FAQ Item 2 -->
              <div class="accordion-item border-0 mb-3 rounded-3 shadow-sm">
                <h2 class="accordion-header">
                  <button class="accordion-button collapsed rounded-3" type="button" data-bs-toggle="collapse" data-bs-target="#faq2" aria-expanded="false" aria-controls="faq2">
                    Do you offer technical support for all subscription plans?
                  </button>
                </h2>
                <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#contactFAQ">
                  <div class="accordion-body">
                    Yes, all Flowkar subscription plans include technical support. However, response times and support channels may vary based on your plan level. Premium plans receive priority support with dedicated account managers.
                  </div>
                </div>
              </div>
              
              <!-- FAQ Item 3 -->
              <div class="accordion-item border-0 mb-3 rounded-3 shadow-sm">
                <h2 class="accordion-header">
                  <button class="accordion-button collapsed rounded-3" type="button" data-bs-toggle="collapse" data-bs-target="#faq3" aria-expanded="false" aria-controls="faq3">
                    How can I request a feature or suggest an improvement?
                  </button>
                </h2>
                <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#contactFAQ">
                  <div class="accordion-body">
                    We welcome feature requests and suggestions! You can submit them through this contact form or <NAME_EMAIL>. Our product team reviews all suggestions and incorporates them into our development roadmap based on user demand and feasibility.
                  </div>
                </div>
              </div>
              
              <!-- FAQ Item 4 -->
              <div class="accordion-item border-0 rounded-3 shadow-sm">
                <h2 class="accordion-header">
                  <button class="accordion-button collapsed rounded-3" type="button" data-bs-toggle="collapse" data-bs-target="#faq4" aria-expanded="false" aria-controls="faq4">
                    Do you offer partnership or affiliate programs?
                  </button>
                </h2>
                <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#contactFAQ">
                  <div class="accordion-body">
                    Yes, we have both partnership and affiliate programs for businesses and individuals who want to collaborate with Flowkar. <NAME_EMAIL> with details about your organization and collaboration ideas, and our partnerships team will get back to you.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="mt-5">
    <div class="container">
      <div class="row bg-light rounded-4 p-4 mb-0">
        <div class="col-md-4 text-center text-md-start mb-4 mb-md-0">
          <img src="../src/assets/logo.svg" alt="Flowkar" class="img-fluid mb-3" width="128">
          <p class="text-muted">
            Flowkar lets you create, schedule, and share content across platforms - helping you grow your audience with ease and efficiency.
          </p>
        </div>
        <div class="col-md-4 text-center text-md-start mb-4 mb-md-0">
          <h5 class="mb-3">Contact Us</h5>
          <p class="text-muted">We are always happy to assist you</p>
          <a href="mailto:<EMAIL>" class="text-muted text-decoration-none">Email: <span class="text-decoration-underline"><EMAIL></span></a>
        </div>
        <div class="col-md-4 text-center text-md-start">
          <h5 class="mb-3">Check it out</h5>
          <ul class="list-unstyled">
            <li><a href="index.html" class="text-muted text-decoration-none">Home</a></li>
            <li><a href="index.html#solutions-section" class="text-muted text-decoration-none">Solutions</a></li>
            <li><a href="aboutus.html" class="text-muted text-decoration-none">About Us</a></li>
            <li><a href="blogs.html" class="text-muted text-decoration-none">Blog</a></li>
            <li><a href="contact-us.html" class="text-muted text-decoration-none">Contact Us</a></li>
          </ul>
        </div>
      </div>
      <div class="row bg-primary rounded-bottom-4 p-3 text-white">
        <div class="col-sm-6 text-center text-sm-start mb-2 mb-sm-0">
          <p class="mb-0 small">© 2025 Flowkar, Inc. - All Rights Reserved</p>
        </div>
        <div class="col-sm-6 text-center text-sm-end">
          <a href="terms.html" class="text-white text-decoration-none small me-3">Terms of Use</a>
          <a href="privacy-policy.html" class="text-white text-decoration-none small">Privacy Policy</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Bootstrap JS Bundle with Popper -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Custom JS -->
  <script src="./js/main.js"></script>
  
  <!-- Form Validation Script -->
  <script>
    // Form validation
    (function() {
      'use strict';
      
      // Fetch all forms that need validation
      var forms = document.querySelectorAll('.needs-validation');
      
      // Loop over them and prevent submission
      Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
          if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
          } else {
            event.preventDefault();
            // Show success message
            alert('Thank you for your message! We will get back to you soon.');
            form.reset();
          }
          
          form.classList.add('was-validated');
        }, false);
      });
    })();
  </script>
</body>
</html>