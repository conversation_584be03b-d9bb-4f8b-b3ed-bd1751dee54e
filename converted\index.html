<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <link rel="icon" type="image/svg+xml" href="../src/assets/logo.svg">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- Resource Hints for Performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="dns-prefetch" href="https://api.flowkar.com">

  <!-- Preload Critical Assets -->
  <link rel="preload" href="../src/assets/logo.svg" as="image" type="image/svg+xml">

  <!-- Cache Control Headers -->
  <meta http-equiv="Cache-Control" content="public, max-age=31536000">

  <!-- Performance Optimization -->
  <meta name="theme-color" content="#563D39">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">

  <title>Flowkar - All-in-One Social Media Management Platform</title>
  <meta name="description" content="Streamline your social media management with Flowkar's all-in-one platform for teams and influencers.">

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  
  <!-- Google Fonts - Figtree -->
  <link href="https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap" rel="stylesheet">
  
  <!-- Custom CSS -->
  <link rel="stylesheet" href="./css/styles.css">
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg fixed-top bg-white shadow-sm">
    <div class="container">
      <!-- Logo -->
      <a class="navbar-brand" href="index.html">
        <img src="../src/assets/logo.svg" alt="Logo" class="img-fluid" width="128">
      </a>
      
      <!-- Mobile Toggle Button -->
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      
      <!-- Navigation Links -->
      <div class="collapse navbar-collapse justify-content-center" id="navbarNav">
        <div class="navbar-nav bg-light-gradient rounded-pill px-4 py-2">
          <a class="nav-link active" href="index.html">Home</a>
          <a class="nav-link" href="#solutions-section">Solutions</a>
          <a class="nav-link" href="blogs.html">Blogs</a>
          <a class="nav-link" href="aboutus.html">About Us</a>
          <a class="nav-link" href="contact-us.html">Contact Us</a>
        </div>
      </div>
      
      <!-- Sign Up and Login Buttons -->
      <div class="d-none d-lg-flex align-items-center gap-3">
        <button class="btn btn-link text-dark fw-medium" onclick="window.location.href='https://app.flowkar.com/sign-up'">
          Sign Up
        </button>
        <button class="btn btn-primary d-flex align-items-center gap-2 px-3 py-2" data-action="login">
          <span>Login</span>
          <img src="../src/assets/Navbar/leftArrow.svg" alt="Left Arrow" width="30" height="30">
        </button>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main>
    <!-- Hero Section -->
    <section class="hero-section py-5 mt-5">
      <div class="container py-5">
        <div class="row align-items-center">
          <div class="col-md-6 text-center text-md-start">
            <h1 class="display-4 fw-bold mb-4">Manage All Your Social Media in One Place</h1>
            <p class="lead mb-4">Flowkar lets you create, schedule, and share content across platforms - helping you grow your audience with ease and efficiency.</p>
            <div class="d-flex flex-wrap gap-3 justify-content-center justify-content-md-start">
              <button class="btn btn-primary btn-lg">Get Started</button>
              <button class="btn btn-outline-secondary btn-lg">Learn More</button>
            </div>
          </div>
          <div class="col-md-6 d-none d-md-block">
            <!-- Desktop Image Grid will be implemented here -->
            <div class="hero-images">
              <!-- Images will be added via CSS/JS -->
            </div>
          </div>
          <!-- Mobile Image Carousel (visible on small screens) -->
          <div class="col-12 d-md-none mt-5">
            <!-- Mobile image carousel will be implemented here -->
          </div>
        </div>
      </div>
    </section>

    <!-- Management Features Section -->
    <section id="solutions-section" class="py-5 bg-light">
      <div class="container py-4">
        <div class="text-center mb-5">
          <h2 class="display-5 fw-bold">Management Features</h2>
          <p class="lead">Powerful tools to streamline your social media workflow</p>
        </div>
        <div class="row g-4">
          <!-- Feature cards will be added here -->
        </div>
      </div>
    </section>

    <!-- Social Media Icons Section -->
    <section class="py-5">
      <div class="container py-4">
        <!-- Social media content will be added here -->
      </div>
    </section>

    <!-- Info Card Section -->
    <section class="py-5">
      <div class="container py-4">
        <!-- Info cards will be added here -->
      </div>
    </section>

    <!-- About Us Hero Section -->
    <section class="py-5 bg-light">
      <div class="container py-4">
        <!-- About us content will be added here -->
      </div>
    </section>

    <!-- Thoughts Section -->
    <section class="py-5">
      <div class="container py-4">
        <!-- Thoughts content will be added here -->
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-5 bg-light">
      <div class="container py-4">
        <!-- Testimonials content will be added here -->
      </div>
    </section>

    <!-- Text Reveal Section -->
    <section class="py-5">
      <div class="container py-4">
        <!-- Text reveal content will be added here -->
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-5 bg-light">
      <div class="container py-4">
        <!-- FAQ content will be added here -->
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="mt-5">
    <div class="container">
      <div class="row bg-light rounded-4 p-4 mb-0">
        <div class="col-md-4 text-center text-md-start mb-4 mb-md-0">
          <img src="../src/assets/logo.svg" alt="Flowkar" class="img-fluid mb-3" width="128">
          <p class="text-muted">
            Flowkar lets you create, schedule, and share content across platforms - helping you grow your audience with ease and efficiency.
          </p>
        </div>
        <div class="col-md-4 text-center text-md-start mb-4 mb-md-0">
          <h5 class="mb-3">Contact Us</h5>
          <p class="text-muted">We are always happy to assist you</p>
          <a href="mailto:<EMAIL>" class="text-muted text-decoration-none">Email: <span class="text-decoration-underline"><EMAIL></span></a>
        </div>
        <div class="col-md-4 text-center text-md-start">
          <h5 class="mb-3">Check it out</h5>
          <ul class="list-unstyled">
            <li><a href="index.html" class="text-muted text-decoration-none">Home</a></li>
            <li><a href="#solutions-section" class="text-muted text-decoration-none">Solutions</a></li>
            <li><a href="aboutus.html" class="text-muted text-decoration-none">About Us</a></li>
            <li><a href="blogs.html" class="text-muted text-decoration-none">Blog</a></li>
            <li><a href="contact-us.html" class="text-muted text-decoration-none">Contact Us</a></li>
          </ul>
        </div>
      </div>
      <div class="row bg-primary rounded-bottom-4 p-3 text-white">
        <div class="col-sm-6 text-center text-sm-start mb-2 mb-sm-0">
          <p class="mb-0 small">© 2025 Flowkar, Inc. - All Rights Reserved</p>
        </div>
        <div class="col-sm-6 text-center text-sm-end">
          <a href="terms.html" class="text-white text-decoration-none small me-3">Terms of Use</a>
          <a href="privacy-policy.html" class="text-white text-decoration-none small">Privacy Policy</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Bootstrap JS Bundle with Popper -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Custom JS -->
  <script src="./js/main.js"></script>
</body>
</html>